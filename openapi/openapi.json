{"openapi": "3.0.1", "info": {"title": "Vehicle Service API", "description": "Microservice for vehicle operations", "contact": {"name": "One App Backend Team", "email": "<EMAIL>"}, "license": {"name": "Copyright (c) 2023. Jaguar Land Rover - All Rights Reserved."}, "version": "3.0.1"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "paths": {"/api/v1/users/me/vehicles/{vehicleId}": {"patch": {"tags": ["Update Vehicle Registration"], "summary": "Update registration number of given vehicle", "description": "Given a ForgeRock Access Token, update registration number of given vehicle.", "operationId": "updateVehicleRegistration", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegistrationUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "Registration number updated."}, "401": {"description": "Unauthorized to access."}, "403": {"description": "Access to vehicles is forbidden with the given access token."}, "500": {"description": "Internal Server Error."}, "502": {"description": "Failed to connect to upstream server."}}}}, "/api/v1/users/me/vehicles": {"get": {"tags": ["Vehicle Details"], "summary": "Get Vehicle List by access token", "description": "Given a ForgeRock Access Token, get the list of vehicles of the user.", "operationId": "getVehicleDetails", "parameters": [{"name": "Authorization", "in": "header", "description": "ForgeRock JWT Access Token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "List of vehicle data received.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Vehicle"}}}}}, "401": {"description": "Unauthorized to access."}, "403": {"description": "Access to vehicles is forbidden with the given access token."}, "404": {"description": "Vehicles not found for given access token."}, "500": {"description": "Internal Server Error."}, "502": {"description": "Bad Gateway. Failed to connect to upstream server."}}}}}, "components": {"schemas": {"RegistrationUpdateRequest": {"type": "object", "properties": {"registration": {"type": "string"}}}, "Images": {"type": "object", "properties": {"primary": {"type": "string", "example": "https://www.acme-corp.com"}, "right_profile": {"type": "string"}}}, "Order": {"type": "object", "properties": {"order_number": {"type": "string", "example": "10001111"}, "order_status": {"type": "string", "example": "ON_WAY_TO_RETAILER"}, "estimated_planned_delivery_date": {"type": "string", "format": "date-time"}, "dealership_reference_number": {"type": "string", "example": "P4038R0626"}, "dealership_contact_number": {"type": "string"}, "shipping_attributes": {"$ref": "#/components/schemas/ShippingAttributes"}}}, "ShippingAttributes": {"type": "object", "properties": {"planned_vessel_name": {"type": "string"}, "actual_vessel_name": {"type": "string"}, "planned_sail_date": {"type": "string", "format": "date-time"}, "shipping_bill_of_landing_id": {"type": "integer", "format": "int32"}, "shipping_bill_of_landing_date": {"type": "string", "format": "date-time"}, "shipping_bill_of_landing_status": {"type": "string"}, "intermediate_destination_id": {"type": "string"}, "planned_intermediate_destination_date": {"type": "string", "format": "date-time"}, "intermediate_destination_date": {"type": "string", "format": "date-time"}, "final_destination_id": {"type": "string"}, "final_destination_description": {"type": "string"}, "final_destination_org_level": {"type": "integer", "format": "int32"}}}, "Vehicle": {"type": "object", "properties": {"vid": {"type": "string", "example": "78af3491-5c12-7ad3-d3d4-02ba17294dfc"}, "vin": {"type": "string", "example": "SALYL2FV0KA225520"}, "brand": {"type": "string", "example": "JAG"}, "model": {"type": "string", "example": "Defender"}, "description": {"type": "string", "example": "Defender 90 D240 (2.0 Diesel SD4 240HP) AWD Auto First Edition"}, "colour": {"type": "string", "example": "Pangea Green"}, "nickname": {"type": "string", "example": "Baby Defender"}, "registration": {"type": "string", "example": "MA22 ABC"}, "images": {"$ref": "#/components/schemas/Images"}, "order": {"$ref": "#/components/schemas/Order"}, "model_year": {"type": "string", "example": "MY21"}, "ownership_type": {"type": "string", "example": "OWNED"}, "fuel_type": {"type": "string", "example": "PHEV", "enum": ["ICE", "BEV", "PHEV", "UNKNOWN"]}, "display_name": {"type": "string", "example": "Range Rover Sport"}}}}}}