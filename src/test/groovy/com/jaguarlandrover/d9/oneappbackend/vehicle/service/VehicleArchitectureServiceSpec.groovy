/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.DigitalVehicleClient
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.DigitalVehicleDataResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleArchitectureType
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.DigitalVehicleException
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.RemoteServiceException
import feign.FeignException

class VehicleArchitectureServiceSpec extends AbstractSpecification {
    ListAppender logAppender

    DigitalVehicleClient dvClient = Mock()
    VehicleArchitectureService vehicleArchitectureService = new VehicleArchitectureService(dvClient)

    def setup() {
        logAppender = setupLogAppender(VehicleArchitectureService)
    }

    def "service returns expected vehicle architecture for valid request"() {
        given: "ForgeRock token and vehicle ID"
        def token = "some-token"
        def vehicleId = "some-vehicle-id"

        and: "we know vehicle architecture type"
        def expectedArchitecture = VehicleArchitectureType.EVA_2

        and: "dv client returns correct vehicle architecture data"
        dvClient.getVehicleData(token, vehicleId, "ASSETS") >> VehicleDataResponse.builder()
                .data(DigitalVehicleDataResponse.builder()
                        .assets(Assets.builder()
                                .vehicleArchitecture(String.valueOf(expectedArchitecture))
                                .build())
                        .build())
                .build()

        when: "service is asked for vehicle architecture"
        def result = vehicleArchitectureService.getVehicleArchitecture(token, vehicleId)

        then: "it returns the correct vehicle architecture type"
        result == expectedArchitecture
    }

    def "service throws DigitalVehicleException when FeignClient fails"() {
        given: "ForgeRock token and vehicle ID"
        def token = "some-token"
        def vehicleId = "some-vehicle-id"

        and: "we have error message"
        def errorMsg = "No vehicle was found with the provided id."

        and: "digital vehicle client throws a FeignException"
        def feignException = Mock(FeignException)
        feignException.getMessage() >> errorMsg
        dvClient.getVehicleData(token, vehicleId, "ASSETS") >> { throw feignException }

        when: "service is asked for vehicle architecture"
        vehicleArchitectureService.getVehicleArchitecture(token, vehicleId)

        then: "it throws a RemoteAPIException with correct log"
        thrown(RemoteServiceException)

        and: "error is logged with correct details"
        def logMessages = filterLogItems(logAppender, Level.ERROR)
        logMessages.any { it.formattedMessage.contains("Digital Vehicle API error: " + errorMsg) }
    }

    def "service throws exception when dv client response is null or doesn't contain the expected data"() {
        given: "ForgeRock token and vehicle ID"
        def token = "some-token"
        def vehicleId = "some-vehicle-id"

        and: "dv client returns a null response"
        dvClient.getVehicleData(token, vehicleId,"ASSETS") >> null

        when: "service is asked for vehicle architecture"
        vehicleArchitectureService.getVehicleArchitecture(token, vehicleId)

        then: "exception was thrown"
        def thrownException = thrown(DigitalVehicleException)
        thrownException.message.contains("Vehicle architecture data is missing")
    }
}
