/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.integration

import ch.qos.logback.classic.Level
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.ArchitectureTypeConverter
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.ExceptionHandler
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabFrTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabTestConstant
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.RemoveVehicleService
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.VehicleArchitectureService
import org.skyscreamer.jsonassert.JSONAssert
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class RemoveVehicleSpecIT extends AbstractSpecificationIT {

    def setup() {
        logAppender = setupLogAppender(RemoveVehicleService)
    }

    def "Remove vehicle request routed correctly to Wireless Car or VCDP depends on vehicle architecture type"() { // happy path scenario

        given: "forge rock token containing user details"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle configured to return architecture type for vehicle"
        configureDigitalVehicleApi("/digitalvehicle/architecture/" + responseFile, forgeRockToken, 200, List.of(OabTestConstant.ASSETS))

        and: "wireless car glue wrapper return OK for delete request"
        configureWirelessCarGlueApi("/wirelesscar/200Response.json", forgeRockToken, 200)

        when: "delete vehicle request was sent"
        def response = mvc.perform(delete(String.format("/api/v1/users/me/vehicles/%s", OabTestConstant.SOME_VEHICLE_IDENTIFIER))
        .header("Authorization", forgeRockToken))

        then: "the response is OK"
        response.andExpect(status().isOk())

        and: "logs saying we calling Wireless Car Glue API"
        def logs = filterLogItems(logAppender, Level.INFO)
        logs.size() == 2
        logs.any { it.getFormattedMessage().contains("Getting vehicle architecture from Digital Vehicle API") }
        logs.any { it.getFormattedMessage().contains(routeLog) }
        logs.any { it.getFormattedMessage().contains(direction) }

        where:
        responseFile                       | routeLog                         | direction
        "assetsEVA_2DataResponse200.json"  | "Vehicle architecture is EVA_2"  | " call Wireless Car Glue wrapper service to remove vehicle"
        "assetsEVA_25DataResponse200.json" | "Vehicle architecture is EVA_25" | " this vehicle is not supported yet"

    }

    def "Unrecognized or empty vehicle architecture type returned from DV API"() { //unhappy path scenario

        given: "forge rock token containing user details"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle configured to return architecture type for vehicle"
        configureDigitalVehicleApi("/digitalvehicle/architecture/" + responseFile, forgeRockToken, 200, List.of(OabTestConstant.ASSETS))

        and: "logs are configured"
        logAppender = setupLogAppender(VehicleArchitectureService, ArchitectureTypeConverter)

        when: "delete vehicle request was sent"
        def response = mvc.perform(delete(String.format("/api/v1/users/me/vehicles/%s", OabTestConstant.SOME_VEHICLE_IDENTIFIER))
                .header("Authorization", forgeRockToken))

        then: "the response has error"
        response.andExpect(statusCode)

        and: "logs are saying unknown or empty architecture type"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.any { it.getFormattedMessage().contains(errorLog) }

        where:
        responseFile                                    | statusCode                       | errorLog
        "assetsNoArchitectureDataResponse200.json"      | status().isNotFound()            | "Vehicle architecture type is null for vehicle ID"
        "assetsUnknownArchitectureDataResponse200.json" | status().isUnprocessableEntity() | "Unknown vehicle architecture"
    }

    def "Returns handled errors from Digital Vehicle API with error message passed"() {

        given: "forge rock token containing user details"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle configured to return not found error"
        configureDigitalVehicleApi("/digitalvehicle/error/" + responseFile, forgeRockToken, errorCode, List.of(OabTestConstant.ASSETS))

        and: "logs are configured"
        logAppender = setupLogAppender(VehicleArchitectureService)

        when: "delete vehicle request was sent"
        def response = mvc.perform(delete(String.format("/api/v1/users/me/vehicles/%s", OabTestConstant.SOME_VEHICLE_IDENTIFIER))
                .header("Authorization", forgeRockToken))

        then: "the response has error"
        response.andExpect(statusCodeExpected)

        and: "the response body as expected"
        def expectedResponse = this.getClass().getResource("/digitalvehicle/error/" + expectedResponseBody).text
        JSONAssert.assertEquals(expectedResponse, response.andReturn().response.contentAsString, true)

        where:
        responseFile                         | errorCode | statusCodeExpected               | errorLogExpected  |  expectedResponseBody
        "vehicleNotFoundFromDVResponse.json" | 404       | status().isNotFound()            | "with status 404" |  "vehicleNotFoundResponse.json"
        "vehicleNotFoundFromDVResponse.json" | 502       | status().isBadGateway()          | "with status 502" |  "badGatewayResponse.json"
        "someErrorFromDVResponse.json"       | 503       | status().isServiceUnavailable()  | "with status 503" |  "remoteServerErrorResponse.json"
    }

    def "Returns handled errors from Wireless Car Glue Wrapper API with error message passed"() {

        given: "forge rock token containing user details"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle configured to return EVA_2 architecture type for vehicle, so we call WC Glue wrapper"
        configureDigitalVehicleApi("/digitalvehicle/architecture/assetsEVA_2DataResponse200.json", forgeRockToken, 200, List.of(OabTestConstant.ASSETS))

        and: "wireless car glue wrapper service returns error"
        configureWirelessCarGlueApi("/wirelesscar/" + errorResponse, forgeRockToken, errorCode)

        and: "logs are configured"
        logAppender = setupLogAppender(ExceptionHandler)

        when: "delete vehicle request was sent"
        def response = mvc.perform(delete(String.format("/api/v1/users/me/vehicles/%s", OabTestConstant.SOME_VEHICLE_IDENTIFIER))
                .header("Authorization", forgeRockToken))

        then: "the response has error"
        response.andExpect(statusCodeExpected)

        and: "logs are saying name of service and error status code"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.any { it.getFormattedMessage().contains("Failed calling remote API: WirelessCar Glue Wrapper API") }
        logs.any { it.getFormattedMessage().contains(errorLogExpected) }

        and: "the response body as expected"
        def expectedResponse = this.getClass().getResource("/wirelesscar/response/" + expectedResponseBody).text
        JSONAssert.assertEquals(expectedResponse, response.andReturn().response.contentAsString, true)

        where:
        errorResponse                 | errorCode | statusCodeExpected              | errorLogExpected  |  expectedResponseBody
        "404WithMessageResponse.json" | 404       | status().isNotFound()           | "with status 404" |  "notFoundResponse.json"
        "noMessageResponse.json"      | 404       | status().isNotFound()           | "with status 404" |  "defaultNotFoundResponse.json"
        "noMessageResponse.json"      | 403       | status().isForbidden()          | "with status 403" |  "defaultErrorResponse.json"
        "noMessageResponse.json"      | 502       | status().isBadGateway()         | "with status 502" |  "badGatewayResponse.json"
        "noMessageResponse.json"      | 503       | status().isServiceUnavailable() | "with status 503" |  "defaultErrorResponse.json"
    }

}
