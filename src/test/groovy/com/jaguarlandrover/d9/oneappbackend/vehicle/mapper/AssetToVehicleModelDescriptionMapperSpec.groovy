/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.config.VehicleProperties
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets

class AssetToVehicleModelDescriptionMapperSpec extends AbstractSpecification {

    private static final MODEL_L461 = "L461"
    private static final MODEL_L460 = "L460"
    private static final MODEL_X761 = "X761"
    private static final RANGE_ROVER = "Range Rover"
    private static final RANGE_ROVE_SPORT = "Range Rover Sport"

    VehicleProperties vehicleProperties = Mock()

    AssetToVehicleModelDescriptionMapper attributeToVehicleModelDescriptionMapper

    def setup() {
        vehicleProperties.getModelDescription() >> [(MODEL_L460): RANGE_ROVER, (MODEL_L461): RANGE_ROVE_SPORT]
        attributeToVehicleModelDescriptionMapper = new AssetToVehicleModelDescriptionMapper(vehicleProperties)
    }

    def "maps attributes to model description correctly"() {
        given: "attributes with model range"
        def attributes = Assets.builder().modelRange(modelRange).build()

        when: "the attributes is sent to the mapper"
        def output = attributeToVehicleModelDescriptionMapper.map(attributes)

        then: "the model description is mapped correctly"
        output == expectedModelDescription

        where:
        modelRange | expectedModelDescription
        MODEL_X761 | MODEL_X761
        MODEL_L460 | RANGE_ROVER
        MODEL_L461 | RANGE_ROVE_SPORT
    }

}
