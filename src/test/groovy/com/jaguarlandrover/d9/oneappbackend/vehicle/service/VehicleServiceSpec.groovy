/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider
import com.jaguarlandrover.d9.oneappbackend.springsecurity.token.data.OabUserData
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.DigitalVehicleClient
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.OrderServiceClient
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.DigitalVehicleDataResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.VehicleOrder
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.AuthorisationException
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException
import com.jaguarlandrover.d9.oneappbackend.vehicle.mapper.AssetsToVehicleMapper
import com.jaguarlandrover.d9.oneappbackend.vehicle.mapper.MainMapper
import com.jaguarlandrover.d9.oneappbackend.vehicle.mapper.OrdersToVehiclesMapper
import feign.FeignException
import io.micrometer.core.instrument.Counter

class VehicleServiceSpec extends AbstractSpecification {

    ListAppender logAppender

    UserDataProvider userDataProvider = Mock()
    IUserIdentityService userIdentityService = Mock()
    DigitalVehicleClient digitalVehicleClient = Mock()
    OrderServiceClient orderServiceClient = Mock()
    OrdersToVehiclesMapper vehicleOrdersToVehiclesMapper = Mock()
    MainMapper globalMapper = Mock()
    CounterService counterService = Mock()
    Counter noVehiclesInTokenCounter = Mock()
    Counter vehiclesRetrievedCounter = Mock()
    Counter errorCounter = Mock()
    AssetsToVehicleMapper assetsToVehicleMapper = Mock()

    VehicleService vehicleService = new VehicleService(
            userDataProvider,
            userIdentityService,
            digitalVehicleClient,
            orderServiceClient,
            true,
            globalMapper,
            counterService,
            assetsToVehicleMapper
    )

    def static final TOKEN = "TOKEN_SAMPLE"
    def static final SVCRM_ID = "some-user-id"
    def static final VEHICLE_ID_1 = "some-vehicle-id-1"
    def static final VEHICLE_ID_2 = "some-vehicle-id-2"
    def static final PARAMS = "ASSETS,IDENTITY"


    def setup() {
        logAppender = setupLogAppender(VehicleService)
        counterService.getNoVehiclesInTokenCounter() >> noVehiclesInTokenCounter
        counterService.getVehiclesRetrievedCounter() >> vehiclesRetrievedCounter
        counterService.getErrorCounter() >> errorCounter
    }

    def "Responds with list of vehicle details for user id"() {

        given: "we know user id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with vehicle data"
        def vehicleData = new VehicleDataResponse(Mock(DigitalVehicleDataResponse))
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> vehicleData

        and: "client responds with vehicle orders"
        def vehicleOrders = [Mock(VehicleOrder)]
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicle orders to the list of vehicles"
        def orders = [Mock(VehicleOrder)]
        vehicleOrdersToVehiclesMapper.map(vehicleOrders) >> orders

        and: "mapper maps vehicle details from digital vehicle data response"
        def vehicle = Mock(Vehicle)
        assetsToVehicleMapper.map(vehicleData) >> vehicle

        and: "mapper maps vehicle data and orders data to vehicle list"
        def matchedVehicles = [Mock(Vehicle)]
        globalMapper.map([vehicle], vehicleOrders) >> matchedVehicles

        when: "token is sent to the service"
        def output = vehicleService.getVehicleDetails(TOKEN)

        then: "service responds with a list of vehicle details"
        output == matchedVehicles
    }

    def "If vehicle details are not available but orders are ok, then responds with partial result"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with 404"
        def clientException = new FeignException(404, "Vehicle not found")
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> { throw clientException }

        and: "client responds with orders"
        def vehicleOrders = [Mock(VehicleOrder)]
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps orders data to vehicle list"
        def vehicles = [Mock(Vehicle)]
        globalMapper.map([], vehicleOrders) >> vehicles

        when: "service is asked for vehicle data"
        def output = vehicleService.getVehicleDetails(TOKEN)

        then: "a response from mapper is returned"
        output == vehicles

        and: "no logs for missing vehicles"
        def errorLogs = filterLogItems(logAppender, Level.WARN)
        errorLogs.size() == 0
    }

    def "204 scenario: logs error if token contains vehicle ids but Digital Vehicle responds with 204"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with 204 empty response"
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> null

        and: "client responds with empty orders"
        def vehicleOrders = []
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicles data and orders data to vehicle list"
        def matchedVehicles = []
        globalMapper.map([], vehicleOrders) >> matchedVehicles

        when: "service is asked for vehicle data"
        def output = vehicleService.getVehicleDetails(TOKEN)

        then: "an empty array was returned"
        def expectedResponse = []
        output == expectedResponse

        and: "no vehicles from digital vehicle api returned is logged"
        def errorLogs = filterLogItems(logAppender, Level.ERROR)
        errorLogs.size() == 1
        errorLogs.get(0).getFormattedMessage().contains("No vehicles were found for user ${SVCRM_ID} with token vehicle attributes")
    }

    def "404 scenario: logs error if token contains vehicle ids but Digital Vehicle responds with 404"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with 404"
        def clientException = new FeignException(404, "Vehicle not found")
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> { throw clientException }

        and: "client responds with empty orders"
        def vehicleOrders = []
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicles data and orders data to vehicle list"
        def matchedVehicles = []
        globalMapper.map([], vehicleOrders) >> matchedVehicles

        when: "service is asked for vehicle data"
        def output = vehicleService.getVehicleDetails(TOKEN)

        then: "an empty array was returned"
        def expectedResponse = []
        output == expectedResponse

        and: "no vehicles from attribute service is logged"
        def errorLogs = filterLogItems(logAppender, Level.ERROR)
        errorLogs.size() == 2
        errorLogs.get(1).getFormattedMessage().contains("No vehicles were found for user ${SVCRM_ID}")

        and: "error counter was increased"
        1 * errorCounter.increment()
    }

    def "Logs warning when no vehicles in fr token"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> []
        userDataProvider.getOabUserData() >> userData

        and: "client responds with 404"
        def clientException = new FeignException(404, "Vehicle not found")
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> { throw clientException }

        and: "client responds with empty orders"
        def vehicleOrders = []
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicle data and orders data to vehicle list"
        def matchedVehicles = []
        globalMapper.map([], vehicleOrders) >> matchedVehicles

        when: "service is asked for vehicle data"
        def output = vehicleService.getVehicleDetails(TOKEN)

        then: "an empty array was returned"
        def expectedResponse = []
        output == expectedResponse

        and: "vehicle missing in the token was logged"
        def logs = filterLogItems(logAppender, Level.WARN)
        logs.size() == 1
        logs.get(0).getFormattedMessage().contains("Missing vehicles in token for user ${SVCRM_ID}")
    }

    def "Logs vehicle ids from token"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle ids"
        userData.getVehicles() >> [VEHICLE_ID_1, VEHICLE_ID_2]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with vehicles data"
        def vehicleDetails_1 = new VehicleDataResponse(Mock(DigitalVehicleDataResponse))
        def vehicleDetails_2 = new VehicleDataResponse(Mock(DigitalVehicleDataResponse))
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> vehicleDetails_1
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_2, PARAMS) >> vehicleDetails_2

        and: "client responds with vehicle orders"
        def vehicleOrders = [Mock(VehicleOrder)]
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicle orders to the list of vehicles"
        def orders = [Mock(VehicleOrder)]
        vehicleOrdersToVehiclesMapper.map(vehicleOrders) >> orders

        and: "mapper maps vehicle details to the list of vehicles"
        def vehicle_1 = Mock(Vehicle)
        def vehicle_2 = Mock(Vehicle)
        assetsToVehicleMapper.map(vehicleDetails_1) >> vehicle_1
        assetsToVehicleMapper.map(vehicleDetails_2) >> vehicle_2

        and: "mapper maps vehicle data and orders data to vehicle list"
        def matchedVehicles = [Mock(Vehicle)]
        globalMapper.map([vehicle_1, vehicle_2], vehicleOrders) >> matchedVehicles

        and: "counter service returns the vehicle retrieved counter"
        def vehiclesRetrievedCounter = Mock(Counter)
        counterService.getVehiclesRetrievedCounter() >> vehiclesRetrievedCounter

        when: "service is asked for vehicle data"
        vehicleService.getVehicleDetails(TOKEN)

        then: "vehicle ids were logged"
        def logs = filterLogItems(logAppender, Level.INFO)
        logs.any {
            it.formattedMessage.contains("Vehicle ids in token: ")
            it.formattedMessage.contains(VEHICLE_ID_1)
            it.formattedMessage.contains(VEHICLE_ID_2)
        }
    }

    def "Increments counter when vehicles are retrieved from Attribute Service"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> [VEHICLE_ID_1, VEHICLE_ID_2]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with vehicle data for both vehicles"
        def vehicleDetails_1 = new VehicleDataResponse(Mock(DigitalVehicleDataResponse))
        def vehicleDetails_2 = new VehicleDataResponse(Mock(DigitalVehicleDataResponse))
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> vehicleDetails_1
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_2, PARAMS) >> vehicleDetails_2

        and: "client responds with vehicle orders"
        def vehicleOrders = [Mock(VehicleOrder)]
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicle details to the list of vehicles"
        def vehicle_1 = Mock(Vehicle)
        def vehicle_2 = Mock(Vehicle)
        assetsToVehicleMapper.map(vehicleDetails_1) >> vehicle_1
        assetsToVehicleMapper.map(vehicleDetails_2) >> vehicle_2

        and: "mapper maps vehicle orders to the list of vehicles"
        def orders = [Mock(VehicleOrder)]
        vehicleOrdersToVehiclesMapper.map(vehicleOrders) >> orders

        and: "mapper maps vehicle data and orders data to vehicle list"
        def matchedVehicles = [Mock(Vehicle)]
        globalMapper.map([vehicle_1, vehicle_2], vehicleOrders) >> matchedVehicles

        when: "service is asked for vehicle data"
        vehicleService.getVehicleDetails(TOKEN)

        then: "counter is incremented with two vehicles"
        1 * vehiclesRetrievedCounter.increment(2.0)
    }

    def "401 & 403 scenario: forwards exception from Digital Vehicle API"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with exception"
        def clientException = new FeignException(exceptionCode, "Forbidden")
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> { throw clientException }

        and: "client responds with empty orders"
        def vehicleOrders = []
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicle data and orders data to vehicle list"
        def matchedVehicles = []
        globalMapper.map([], vehicleOrders) >> matchedVehicles

        when: "service is asked for vehicle data"
        vehicleService.getVehicleDetails(TOKEN)

        then: "an exception was thrown"
        def exception = thrown(thrown)
        exception.cause == clientException

        and: "error counter was increased"
        1 * errorCounter.increment()

        where:
        exceptionCode | thrown
        401           | AuthorisationException
        403           | ForbiddenException
    }

    def "Responds with an empty array when Digital Vehicle API has internal server error"() {

        given: "we know svcrm id"
        def userData = Mock(OabUserData)
        userIdentityService.getUserId() >> SVCRM_ID

        and: "we know vehicle id"
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "client responds with 500"
        def clientException = new FeignException(500, "Internal Server Error")
        digitalVehicleClient.getVehicleData(TOKEN, VEHICLE_ID_1, PARAMS) >> { throw clientException }

        and: "client responds with empty orders"
        def vehicleOrders = []
        orderServiceClient.getAllOrdersByUserId(TOKEN, SVCRM_ID) >> vehicleOrders

        and: "mapper maps vehicle data and orders data to vehicle list"
        def matchedVehicles = []
        globalMapper.map([], vehicleOrders) >> matchedVehicles

        when: "service is asked for vehicle data"
        def output = vehicleService.getVehicleDetails(TOKEN)

        then: "an exception was thrown"
        def expectedResponse = []
        output == expectedResponse
    }
}
