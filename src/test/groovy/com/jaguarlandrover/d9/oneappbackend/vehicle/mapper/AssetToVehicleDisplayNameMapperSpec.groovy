/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Identity

class AssetToVehicleDisplayNameMapperSpec extends AbstractSpecification {

    private static final TEST_REGISTRATION_NUMBER = "AB12CDE"
    private static final TEST_MODEL_DESCRIPTION = "Range Rover"
    private static final TEST_MODEL_RANGE = "X761"
    private static final TEST_IDENTIFIER = "433e0622-403d-4311-9ba9-9e21a9baa68e"

    AssetToVehicleModelDescriptionMapper attributeToVehicleModelDescriptionMapper = Mock()

    AssetToVehicleDisplayNameMapper attributeToVehicleDisplayNameMapper = new AssetToVehicleDisplayNameMapper(attributeToVehicleModelDescriptionMapper)

    def "maps attributes to display name correctly"() {
        given: "attributes"
        def assets = Assets.builder()
                .modelRange(modelRange)
                .build()
        and: "identity"
        def identity = Identity.builder()
                .vehicleReg(registrationNumber)
                .uniqueId(identifier)
                .build()

        and: "model description"
        attributeToVehicleModelDescriptionMapper.map(assets) >> modelDescription

        when: "the attributes is sent to the mapper"
        def output = attributeToVehicleDisplayNameMapper.map(assets, identity)

        then: "the display name is mapped correctly"
        output == expectedDisplayName

        where:
        registrationNumber       | modelDescription       | modelRange       | identifier      | expectedDisplayName
        TEST_REGISTRATION_NUMBER | TEST_MODEL_DESCRIPTION | TEST_MODEL_RANGE | TEST_IDENTIFIER | TEST_REGISTRATION_NUMBER
        null                     | TEST_MODEL_DESCRIPTION | TEST_MODEL_RANGE | TEST_IDENTIFIER | TEST_MODEL_DESCRIPTION
        null                     | null                   | TEST_MODEL_RANGE | TEST_IDENTIFIER | TEST_MODEL_RANGE
        null                     | null                   | null             | TEST_IDENTIFIER | TEST_IDENTIFIER
        TEST_REGISTRATION_NUMBER | null                   | null             | null            | TEST_REGISTRATION_NUMBER
        null                     | TEST_MODEL_DESCRIPTION | null             | null            | TEST_MODEL_DESCRIPTION
        null                     | null                   | TEST_MODEL_RANGE | null            | TEST_MODEL_RANGE
        null                     | null                   | null             | null            | null
        ""                       | ""                     | ""               | ""              | null
    }

}
