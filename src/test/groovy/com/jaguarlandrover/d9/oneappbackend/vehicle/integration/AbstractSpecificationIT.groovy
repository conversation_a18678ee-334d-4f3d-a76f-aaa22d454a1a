/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.integration

import ch.qos.logback.core.read.ListAppender
import com.github.tomakehurst.wiremock.WireMockServer
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.VehicleApplication
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabTestConstant
import com.jaguarlandrover.d9.oneappbackend.vehicle.repository.VehicleImagesRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.web.servlet.MockMvc
import org.testcontainers.containers.localstack.LocalStackContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName
import spock.lang.Shared

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static com.github.tomakehurst.wiremock.client.WireMock.delete as wiremockDelete
import static com.github.tomakehurst.wiremock.client.WireMock.get as wiremockGet
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig
import static org.testcontainers.containers.localstack.LocalStackContainer.Service.DYNAMODB

@DirtiesContext
@AutoConfigureMockMvc
@ActiveProfiles(["test"])
@SpringBootTest(classes = VehicleApplication.class)
class AbstractSpecificationIT extends AbstractSpecification {

    public static String REQUEST_ID_VALUE = "some-request-id-from-api-gateway"

    @Shared
    WireMockServer remoteApiWireMockServerForVehicleOrders
    @Shared
    WireMockServer remoteApiWireMockServerForImages
    @Shared
    WireMockServer remoteApiWireMockServerForForgeRock
    @Shared
    WireMockServer remoteApiWireMockServerForDigitalVehicle
    @Shared
    WireMockServer remoteApiWireMockServerForWCGlueWrapper

    @Autowired
    MockMvc mvc

    @Autowired
    VehicleImagesRepository repository

    static int DYNAMODB_PORT = 4566

    ListAppender logAppender

    def setup() {

        remoteApiWireMockServerForVehicleOrders = new WireMockServer(wireMockConfig().port(8082))
        remoteApiWireMockServerForVehicleOrders.start()

        remoteApiWireMockServerForForgeRock = new WireMockServer(wireMockConfig().port(8083))
        remoteApiWireMockServerForForgeRock.start()

        remoteApiWireMockServerForImages = new WireMockServer(wireMockConfig().port(8084))
        remoteApiWireMockServerForImages.start()

        remoteApiWireMockServerForDigitalVehicle = new WireMockServer(wireMockConfig().port(8085))
        remoteApiWireMockServerForDigitalVehicle.start()

        remoteApiWireMockServerForWCGlueWrapper = new WireMockServer(wireMockConfig().port(8086))
        remoteApiWireMockServerForWCGlueWrapper.start()
    }

    def cleanup() {

        remoteApiWireMockServerForVehicleOrders.resetAll()
        remoteApiWireMockServerForVehicleOrders.stop()

        remoteApiWireMockServerForForgeRock.resetAll()
        remoteApiWireMockServerForForgeRock.stop()

        remoteApiWireMockServerForImages.resetAll()
        remoteApiWireMockServerForImages.stop()

        remoteApiWireMockServerForDigitalVehicle.resetAll()
        remoteApiWireMockServerForDigitalVehicle.stop()

        remoteApiWireMockServerForWCGlueWrapper.resetAll()
        remoteApiWireMockServerForWCGlueWrapper.stop()
    }

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("aws.accessKey", { -> localStackContainer.getAccessKey() })
        registry.add("aws.secretKey", { -> localStackContainer.getSecretKey() })
        registry.add("aws.port", { -> localStackContainer.getMappedPort(DYNAMODB_PORT) })
        registry.add("aws.dynamoDB.url", { -> localStackContainer.getEndpoint() })
    }

    static LocalStackContainer localStackContainer =
            new LocalStackContainer(DockerImageName.parse("localstack/localstack:2.1.0"))
                    .withServices(DYNAMODB)
                    .waitingFor(Wait.forHealthcheck())

    def setupSpec() {
        localStackContainer.start()
    }

    def cleanupSpec() {
        localStackContainer.stop()
    }

    def stopWireMockServerForDigitalVehicle() {
        remoteApiWireMockServerForDigitalVehicle.stop()
    }

    void configureForgeRockApi() {
        remoteApiWireMockServerForForgeRock.stubFor(wiremockGet(urlPathEqualTo("/gateway/oauth2/realms/root/realms/customer/connect/jwk_uri"))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource("/security/fr-key-response.json").text)
                        .withStatus(200)))
    }

    void configureDigitalVehicleApiRegistrationUpdate(String forgeRockToken) {
        remoteApiWireMockServerForDigitalVehicle.stubFor(patch(urlPathEqualTo("/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER + "/identity/reg-number"))
                .withHeader("Authorization", equalTo(OabTestConstant.BEARER + forgeRockToken))
                .withRequestBody(equalToJson(this.getClass().getResource("/digitalvehicle/registration/updateRegistrationNumber.json").text))
                .willReturn(aResponse()
                        .withStatus(204)))
    }

    void configureOrdersApi(String resourceName = "/orders/vehicleOrdersResponse2ordersWithNullVin.json") {
        remoteApiWireMockServerForVehicleOrders.stubFor(wiremockGet(urlPathTemplate("/api/v1/users/{userId}/orders"))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource(resourceName).text)
                        .withStatus(200)))
    }

    void configureImagesApiForOrderImage(String resourceName, int status) {
        remoteApiWireMockServerForImages.stubFor(wiremockGet(urlPathTemplate("/api/v1/images/" + TEST_ORDER_NUMBER))
                .withQueryParam("viewAngle", equalTo("TOP"))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource(resourceName).text)
                        .withStatus(status)))

        remoteApiWireMockServerForImages.stubFor(wiremockGet(urlPathTemplate("/api/v1/images/" + TEST_ORDER_NUMBER_2))
                .withQueryParam("viewAngle", equalTo("TOP"))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource(resourceName).text)
                        .withStatus(status)))
    }

    void configureImagesApiForGenericImage(String resourceName, int status) {
        remoteApiWireMockServerForImages.stubFor(wiremockGet(urlPathTemplate("/api/v1/images/generic"))
                .withQueryParam("model", equalTo(TEST_VEHICLE_MODEL))
                .withQueryParam("colour", equalTo(TEST_VEHICLE_COLOUR))
                .withQueryParam("viewAngle", equalTo("TOP"))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource(resourceName).text)
                        .withStatus(status)))
    }

    void configureImagesApiForEmptyGenericImageResponse(int status) {
        remoteApiWireMockServerForImages.stubFor(wiremockGet(urlPathTemplate("/api/v1/images/generic"))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withStatus(status)))
    }

    void configureDigitalVehicleApi(String assetsDataResponse, String frToken, int status, List<String> params) {
        remoteApiWireMockServerForDigitalVehicle.stubFor(wiremockGet(urlPathEqualTo("/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER))
                .withHeader("Authorization", containing(frToken))
                .withQueryParam("include", equalTo(String.join(',', params)))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource(assetsDataResponse).text)
                        .withStatus(status)))
    }

    void configureDigitalVehicleApiByID(String assetsDataResponse, String frToken, String vehicleId) {
        List<String> params = List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY)
        remoteApiWireMockServerForDigitalVehicle.stubFor(wiremockGet(urlPathEqualTo("/vehicles/" + vehicleId))
                .withHeader("Authorization", containing(frToken))
                .withQueryParam("include", equalTo(String.join(',', params)))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource(assetsDataResponse).text)
                        .withStatus(200)))
    }

    void configureWirelessCarGlueApi(String wirelessCarResponse, String frToken, int status) {
        remoteApiWireMockServerForWCGlueWrapper.stubFor(wiremockDelete(urlPathEqualTo("/api/v1/users/me/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER))
                .withHeader("Authorization", containing(frToken))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withBody(this.getClass().getResource(wirelessCarResponse).text)
                        .withStatus(status)))
    }
}
