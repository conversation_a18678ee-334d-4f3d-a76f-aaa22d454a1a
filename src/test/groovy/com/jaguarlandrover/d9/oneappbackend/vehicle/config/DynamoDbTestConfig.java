/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.config;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput;
import com.amazonaws.services.dynamodbv2.util.TableUtils;
import com.jaguarlandrover.d9.oneappbackend.vehicle.repository.VehicleImages;
import java.util.List;
import org.socialsignin.spring.data.dynamodb.repository.config.EnableDynamoDBRepositories;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("test")
@EnableDynamoDBRepositories(basePackages = "com.jaguarlandrover.d9.oneappbackend.vehicle"
    + ".repository")
public class DynamoDbTestConfig {

  public static final String REGION = "eu-west-2";

  @Value("${aws.dynamoDB.url}")
  private String awsDynamoDbUrl;

  @Value("${aws.accessKey}")
  private String awsAccessKey;

  @Value("${aws.secretKey}")
  private String awsSecretKey;

  private final List<Class<?>> entities = List.of(
      VehicleImages.class
  );

  @Bean
  public AmazonDynamoDB amazonDynamoDB() {
    AmazonDynamoDB amazonDynamoDB = amazonDynamoDBClientBuilder().build();
    entities.forEach(c -> initialiseDynamoDbTables(amazonDynamoDB, c));

    return amazonDynamoDB;
  }

  private AmazonDynamoDBClientBuilder amazonDynamoDBClientBuilder() {

    return AmazonDynamoDBClientBuilder.standard()
        .withCredentials(new AWSStaticCredentialsProvider(amazonAWSCredentials()))
        .withEndpointConfiguration(endpointConfiguration());
  }

  private AWSCredentials amazonAWSCredentials() {
    return new BasicAWSCredentials(awsAccessKey, awsSecretKey);
  }

  private AwsClientBuilder.EndpointConfiguration endpointConfiguration() {
    return new AwsClientBuilder.EndpointConfiguration(awsDynamoDbUrl, REGION);
  }

  private void initialiseDynamoDbTables(AmazonDynamoDB amazonDynamoDB, Class<?> clazz) {

    DynamoDBMapper dynamoDBMapper = new DynamoDBMapper(amazonDynamoDB);
    CreateTableRequest tableRequest;

    tableRequest = dynamoDBMapper.generateCreateTableRequest(clazz);
    tableRequest.setProvisionedThroughput(new ProvisionedThroughput(1L, 1L));
    TableUtils.createTableIfNotExists(amazonDynamoDB, tableRequest);
  }
}
