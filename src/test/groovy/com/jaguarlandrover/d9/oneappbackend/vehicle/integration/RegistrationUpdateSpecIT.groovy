/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.integration

import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabFrTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabTestConstant
import org.springframework.http.MediaType

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch as mockMvcPatch
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class RegistrationUpdateSpecIT extends AbstractSpecificationIT {

    def "Updates vehicle registration"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApiRegistrationUpdate(forgeRockToken)

        when: "update endpoint is called"
        def response = mvc.perform(mockMvcPatch("/api/v1/users/me/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER)
                .header("Authorization", "Bearer " + forgeRockToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(this.getClass().getResource("/vehicles/request/updateRegistration.json").text))

        then: "the response is OK"
        response.andExpect(status().is2xxSuccessful())

        and: "digital vehicle API was called to update vehicle"
        remoteApiWireMockServerForDigitalVehicle.verify(1, patchRequestedFor(urlEqualTo("/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER + "/identity/reg-number"))
                .withRequestBody(equalToJson(this.getClass().getResource("/digitalvehicle/registration/updateRegistrationNumber.json").text))
        )

    }

    def "Updates vehicle registration returns forbidden if vehicle id requested in not in FR entitlement"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApiRegistrationUpdate(forgeRockToken)

        when: "update endpoint is called with WRONG VEHICLE ID"
        def response = mvc.perform(mockMvcPatch("/api/v1/users/me/vehicles/" + OabTestConstant.SOME_OTHER_VEHICLE_ID_VALUE)
                .header("Authorization", "Bearer " + forgeRockToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(this.getClass().getResource("/vehicles/request/updateRegistration.json").text))

        then: "the response is forbidden"
        response.andExpect(status().isForbidden())

        and: "digital vehicle API is not called to update vehicle"
        remoteApiWireMockServerForDigitalVehicle.verify(0, patchRequestedFor(urlEqualTo("/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER + "/identity/reg-number"))
                .withRequestBody(equalToJson(this.getClass().getResource("/digitalvehicle/registration/updateRegistrationNumber.json").text))
        )
    }

    def "Updates vehicle registration returns bad request if registration number is null"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        when: "update endpoint is called"
        def response = mvc.perform(mockMvcPatch("/api/v1/users/me/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER)
                .header("Authorization", "Bearer " + forgeRockToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(this.getClass().getResource(requestBodyFile).text))

        then: "the response is bad request"
        response.andExpect(status().isBadRequest())

        where:
        requestBodyFile                                     | _
        "/vehicles/request/updateEmptyRegistration.json"    | _
        "/vehicles/request/updateNullRegistration.json"     | _

    }

    def "Returns 502 when fails to connect to Digital Vehicle API for updating vehicle registration"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "Digital Vehicle server is not reachable"
        stopWireMockServerForDigitalVehicle()

        when: "update endpoint is called"
        def response = mvc.perform(mockMvcPatch("/api/v1/users/me/vehicles/" + OabTestConstant.SOME_VEHICLE_IDENTIFIER)
                .header("Authorization", "Bearer " + forgeRockToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(this.getClass().getResource("/vehicles/request/updateRegistration.json").text))

        then: "The response expected is 502"
        response.andExpect(status().is5xxServerError())
        response.andExpect(status().is(502))

        and: "Expected response body"
        def expectedResponseBody = this.getClass().getResource("/vehicles/502Response.json").text
        response.andExpect(content().json(expectedResponseBody as String))
    }
}
