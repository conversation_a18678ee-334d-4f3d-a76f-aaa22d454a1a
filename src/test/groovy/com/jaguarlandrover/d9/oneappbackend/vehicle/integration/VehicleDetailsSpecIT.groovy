/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.integration

import ch.qos.logback.classic.Level
import com.jaguarlandrover.d9.oneappbackend.vehicle.config.OabLogInterceptor
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.GlobalExceptionHandler.RestErrorResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabFrTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabTestConstant
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.VehicleService
import org.skyscreamer.jsonassert.JSONAssert
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static com.jaguarlandrover.d9.oneappbackend.vehicle.config.OabLogInterceptor.REQUEST_ID_HEADER_NAME
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get as mockMvcGet
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class VehicleDetailsSpecIT extends AbstractSpecificationIT {

    ObjectMapper objectMapper = new ObjectMapper()

    def setup() {
        logAppender = setupLogAppender(VehicleService.class)
    }

    def "Returns available data from upstream services"() {

        given: "forge rock token containing user details and two vehicle ids"
        def forgeRockToken = OabFrTokenTestHelper.tokenTwoAttributes()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured for both vehicles"
        configureDigitalVehicleApiByID("/digitalvehicle/data/vehicleAttributes.json", forgeRockToken, OabTestConstant.VEHICLE_ID_1)
        configureDigitalVehicleApiByID("/digitalvehicle/data/vehicleAttributes2.json", forgeRockToken, OabTestConstant.VEHICLE_ID_2)

        and: "vehicle orders api is configured"
        configureOrdersApi(orders)

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", 200)

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource(expectedOutputFile).text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)

        where:
        orders                                                 | expectedOutputFile
        "/orders/vehicleOrdersResponse2orders.json"            | "/vehicles/response/vehicles1order.json"
        "/orders/vehicleOrdersResponse2ordersWithNullVin.json" | "/vehicles/response/vehicles2orders.json"
    }

    def "Returns no data when SVCRM ID missing"() {

        given: "forge rock token containing userdetails id"
        def forgeRockToken = OabFrTokenTestHelper.tokenPrincipalIdOnly()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/data/vehicleAttributes.json", forgeRockToken, 200, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi()

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())
    }

    def "Logs missing vehicles in FR token"() {

        given: "forge rock token containing userdetails id"
        def forgeRockToken = OabFrTokenTestHelper.tokenEmptyAttributes()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/data/vehicleAttributes.json", forgeRockToken, 200, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi()

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", 200)

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        and: "repository doesn't have any images"
        repository.deleteAll()

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", 200)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "vehicle missing was logged"
        def logs = filterLogItems(logAppender, Level.WARN)
        logs.size() == 1
        logs.any { it.getFormattedMessage().contains("Missing vehicles in token for user") }
    }

    def "Logs when no vehicles from FR token found in Digital Vehicle"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 204"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 204, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .contentType("application/json")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 200"
        response.andExpect(status().is2xxSuccessful())
        response.andExpect(status().is(200))

        and: "Expected empty response body"
        def expectedResponseBody = []
        response.andExpect(content().json(expectedResponseBody as String))

        and: "there is no vehicle missing log"
        def warnLogs = filterLogItems(logAppender, Level.WARN)
        warnLogs.size() == 0

        and: "no vehicles from attribute service is logged"
        def errorLogs = filterLogItems(logAppender, Level.ERROR)
        errorLogs.get(0).getFormattedMessage().contains("No vehicles were found for user ${OabTestConstant.SOME_SVCRM_ID} with token vehicle attributes: [${OabTestConstant.SOME_VEHICLE_IDENTIFIER}]")
    }

    def "Returns 200 response when Digital Vehicle API returns 204"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 204"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 204, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .contentType("application/json")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 200"
        response.andExpect(status().is2xxSuccessful())
        response.andExpect(status().is(200))

        and: "Expected empty response body"
        def expectedResponseBody = []
        response.andExpect(content().json(expectedResponseBody as String))

        and: "there is no vehicle missing log"
        def warnLogs = filterLogItems(logAppender, Level.WARN)
        warnLogs.size() == 0

        and: "no vehicles from attribute service is logged"
        def errorLogs = filterLogItems(logAppender, Level.ERROR)
        errorLogs.get(0).getFormattedMessage().contains("No vehicles were found for user ${OabTestConstant.SOME_SVCRM_ID} with token vehicle attributes: [${OabTestConstant.SOME_VEHICLE_IDENTIFIER}]")
    }

    def "Returns 200 response when Digital Vehicle returns 404 vehicle not found"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 404"
        configureDigitalVehicleApi("/digitalvehicle/error/vehicleNotFoundFromDVResponse.json", forgeRockToken, 404, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .contentType("application/json")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 200"
        response.andExpect(status().is2xxSuccessful())
        response.andExpect(status().is(200))

        and: "Expected empty response body"
        def expectedResponseBody = []
        response.andExpect(content().json(expectedResponseBody as String))

        and: "there is no vehicle missing log"
        def warnLogs = filterLogItems(logAppender, Level.WARN)
        warnLogs.size() == 0

        and: "no vehicles from attribute service is logged"
        def errorLogs = filterLogItems(logAppender, Level.ERROR)
        errorLogs.get(1).getFormattedMessage().contains("No vehicles were found for user ${OabTestConstant.SOME_SVCRM_ID} with token vehicle attributes: [${OabTestConstant.SOME_VEHICLE_IDENTIFIER}]")
    }

    def "Returns 200 if errors coming from orders image API"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 404, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi("/orders/vehicleOrdersResponse1order.json")

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", responseCode)

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(responseCode)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/noVehicle1orderNoImage.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, true)

        where:
        responseCode | _
        400          | _
        404          | _
        500          | _
        503          | _
    }

    def "Returns 200 if errors coming from generic image API"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/data/vehicleAttributes1vehicleX761.json", forgeRockToken, 200, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi("/orders/vehicleOrdersResponse1order.json")

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", 200)

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(responseCode)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/vehicleNoOrdersNoImage.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)

        where:
        responseCode | _
        400          | _
        404          | _
        500          | _
        503          | _
    }

    def "Returns orders if vehicles not found"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 404"
        configureDigitalVehicleApi("/digitalvehicle/error/vehicleNotFoundResponse.json", forgeRockToken, 404, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi("/orders/vehicleOrdersResponse1order.json")

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", 200)

        and: "repository doesn't have any images"
        repository.deleteAll()

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .contentType("application/json")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 200"
        response.andExpect(status().is(200))

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/noVehicle1orderNoImage.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, true)

        and: "there is no vehicle missing log"
        def warnLogs = filterLogItems(logAppender, Level.WARN)
        warnLogs.size() == 0

        and: "no vehicles from attribute service is logged"
        def errorLogs = filterLogItems(logAppender, Level.ERROR)
        errorLogs.get(1).getFormattedMessage().contains("No vehicles were found for user ${OabTestConstant.SOME_SVCRM_ID} with token vehicle attributes: [${OabTestConstant.SOME_VEHICLE_IDENTIFIER}]")
    }

    def "Returns 200 response when Digital Vehicle returns 400 bad request"() {

        given: "forge rock token containing userdetails id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 400"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 400, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .contentType("application/json")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 200"
        response.andExpect(status().is2xxSuccessful())
        response.andExpect(status().is(200))

        and: "Expected empty response body"
        def expectedResponseBody = []
        response.andExpect(content().json(expectedResponseBody as String))
    }

    def "Returns 401 response when remote api return 401 response i.e. unauthorized"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 401"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 401, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 401"
        response.andExpect(status().is4xxClientError())
    }

    def "Returns 403 response when remote api return 403 response i.e. forbidden access"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 403"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 403, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 403"
        response.andExpect(status().is4xxClientError())
        def result = response.andReturn()

        and: "Expected response body with error message"
        def expectedResponse = RestErrorResponse.builder()
                .message(ForbiddenException.MESSAGE)
                .build()
        response.andExpect(content().json(objectMapper.writeValueAsString(expectedResponse)))
    }

    def "Returns 401 response when Authorization header is missing in the request"() {

        given: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "forge rock token containing user id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "digital vehicle api is configured to return 401"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 401, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles"))

        then: "The response expected is 401"
        response.andExpect(status().is4xxClientError())
    }

    def "Returns 502 response when fail to connect to Digital Vehicle API"() {

        given: "forge rock token containing user id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "Vehicle Attribute server is not reachable"
        stopWireMockServerForDigitalVehicle()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .contentType("application/json")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 502"
        response.andExpect(status().is5xxServerError())
        response.andExpect(status().is(502))

        and: "Expected response body"
        def expectedResponseBody = this.getClass().getResource("/vehicles/502Response.json").text
        response.andExpect(content().json(expectedResponseBody as String))
    }

    def "Returns 200 response when remote api return 500 response"() {

        given: "forge rock token containing userdetails id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured to return 500"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 500, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response expected is 200"
        response.andExpect(status().is2xxSuccessful())
        response.andExpect(status().is(200))
        and: "Expected empty response body"

        def expectedResponseBody = []
        response.andExpect(content().json(expectedResponseBody as String))
    }

    def "Adds request id property to logs"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 404, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi("/orders/vehicleOrdersResponse1order.json")

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", 200)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken)
                .header(REQUEST_ID_HEADER_NAME, REQUEST_ID_VALUE))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "logs contain request id"
        def logs = filterLogItems(logAppender, Level.INFO)
        def loggingEvent = logs.get(0)
        loggingEvent.getMDCPropertyMap().get(OabLogInterceptor.REQUEST_ID_PROPERTY_NAME) == REQUEST_ID_VALUE

        and: "header was set in the call to the order service"
        remoteApiWireMockServerForVehicleOrders.verify(getRequestedFor(urlEqualTo("/api/v1/users/${OabTestConstant.SOME_SVCRM_ID}/orders"))
                .withHeader(REQUEST_ID_HEADER_NAME, equalTo(REQUEST_ID_VALUE)))
    }
}
