/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.ImageClient
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.image.ImageResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.repository.VehicleImages
import com.jaguarlandrover.d9.oneappbackend.vehicle.repository.VehicleImagesRepository
import feign.FeignException

class ImagesServiceSpec extends AbstractSpecification {

    VehicleImagesRepository vehicleImagesRepository = Mock()
    ImageClient imageClient = Mock()
    ImagesService imagesService = new ImagesService(vehicleImagesRepository, imageClient)

    static def TOP = "TOP"
    ListAppender logAppender

    def setup() {
        logAppender = setupLogAppender(ImagesService.class)
    }

    //region Order Id Image

    def "responds with image from repository"() {

        given: "an order number"
        TEST_ORDER_NUMBER

        and: "image url"
        TEST_VEHICLE_IMAGE_URL

        and: "entity in repository"
        def vehicleImages = VehicleImages.builder().topViewImageUrl(TEST_VEHICLE_IMAGE_URL).build()

        and: "repository has available image"
        vehicleImagesRepository.findById(TEST_ORDER_NUMBER) >> Optional.of(vehicleImages)

        when: "service is asked for that"
        def output = imagesService.findVehicleImageUrl(TEST_ORDER_NUMBER)

        then: "it responds with it"
        output == Optional.of(TEST_VEHICLE_IMAGE_URL)
    }

    def "responds with image from remote API if not found in repository and saves it in repo"() {

        given: "an order number"
        TEST_ORDER_NUMBER

        and: "repository has no image"
        vehicleImagesRepository.findById(TEST_ORDER_NUMBER) >> repositoryResponse

        and: "client responds with image"
        imageClient.getTopImageForOrderNumber(TEST_ORDER_NUMBER, TOP) >> [ImageResponse.builder().url(TEST_VEHICLE_IMAGE_URL).build()]

        when: "service is asked for that"
        def output = imagesService.findVehicleImageUrl(TEST_ORDER_NUMBER)

        then: "it responds with it"
        output == Optional.of(TEST_VEHICLE_IMAGE_URL)

        and: "there is log for vehicle image not found in repository"
        def logs = filterLogItems(logAppender, Level.INFO)
        logs.size() == 1
        logs.get(0).formattedMessage == "Image not found in storage for order ${TEST_ORDER_NUMBER}"

        and: "image was saved in repository"
        1 * vehicleImagesRepository.save(
                VehicleImages.builder().orderId(TEST_ORDER_NUMBER).topViewImageUrl(TEST_VEHICLE_IMAGE_URL).build()
        )

        where:
        repositoryResponse                           | _
        Optional.empty()                             | _
        Optional.of(VehicleImages.builder().build()) | _
    }

    def "responds with empty optional if image not found"() {

        given: "an order number"
        TEST_ORDER_NUMBER

        and: "repository has no image"
        vehicleImagesRepository.findById(TEST_ORDER_NUMBER) >> repositoryResponse

        and: "client responds with image"
        imageClient.getTopImageForOrderNumber(TEST_ORDER_NUMBER, TOP) >> clientResponse

        when: "service is asked for that"
        def output = imagesService.findVehicleImageUrl(TEST_ORDER_NUMBER)

        then: "it responds with empty optional"
        output == Optional.empty()

        and: "there is log for missing vehicle image"
        def logs = filterLogItems(logAppender, Level.INFO)
        logs.size() == 2
        logs.get(0).formattedMessage == "Image not found in storage for order ${TEST_ORDER_NUMBER}"
        logs.get(1).formattedMessage == "Image not returned from image service for order ${TEST_ORDER_NUMBER}"

        where:
        repositoryResponse                           | clientResponse
        Optional.empty()                             | null
        Optional.of(VehicleImages.builder().build()) | []
        Optional.empty()                             | []
        Optional.of(VehicleImages.builder().build()) | null
    }

    def "handles errors from remote API"() {

        given: "an order number"
        TEST_ORDER_NUMBER

        and: "repository has no image"
        vehicleImagesRepository.findById(TEST_ORDER_NUMBER) >> Optional.empty()

        and: "client throws an exception"
        def feignException = Mock(FeignException)
        feignException.status() >> errorCode
        imageClient.getTopImageForOrderNumber(TEST_ORDER_NUMBER, TOP) >> { throw feignException }

        when: "service is asked for that"
        def output = imagesService.findVehicleImageUrl(TEST_ORDER_NUMBER)

        then: "it responds with empty optional"
        output == Optional.empty()

        and: "there is log for error"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.get(0).formattedMessage == "Error communicating with Images API: ${errorCode} response for order ${TEST_ORDER_NUMBER}"

        where:
        errorCode | _
        400       | _
        404       | _
        500       | _
        503       | _
    }

    //endregion

    //region Generic Id Image

    def "responds with generic image from client"() {

        given: "a model"
        TEST_VEHICLE_MODEL

        and: "colour"
        TEST_VEHICLE_COLOUR

        and: "image url"
        TEST_VEHICLE_IMAGE_URL

        and: "client responds with image"
        imageClient.getGenericImage(TEST_VEHICLE_MODEL, TEST_VEHICLE_COLOUR, TOP) >> genericImageResponse

        when: "service is asked for that"
        def output = imagesService.findVehicleImageUrl(TEST_VEHICLE_MODEL, TEST_VEHICLE_COLOUR)

        then: "it responds with it"
        output == expectedOutput

        and: "expected logs"
        def logs = filterLogItems(logAppender, Level.INFO)
        expectedLogs.forEach { String expectedLog ->
            assert logs.any { it.formattedMessage.contains(expectedLog) }
        }

        where:
        genericImageResponse                                                     | expectedOutput
        Optional.of(ImageResponse.builder().url(TEST_VEHICLE_IMAGE_URL).build()) | Optional.of(TEST_VEHICLE_IMAGE_URL)
        Optional.of(ImageResponse.builder().url(null).build())                   | Optional.empty()
        Optional.empty()                                                         | Optional.empty()
        __
        expectedLogs          | _
        ["Image received"]    | _
        ["No image received"] | _
        ["No image received"] | _
    }

    def "handles errors from generic image API"() {

        given: "a model"
        TEST_VEHICLE_MODEL

        and: "colour"
        TEST_VEHICLE_COLOUR

        and: "image url"
        TEST_VEHICLE_IMAGE_URL

        and: "client throws an exception"
        def feignException = Mock(FeignException)
        feignException.status() >> errorCode
        imageClient.getGenericImage(TEST_VEHICLE_MODEL, TEST_VEHICLE_COLOUR, TOP) >> { throw feignException }

        when: "service is asked for that"
        def output = imagesService.findVehicleImageUrl(TEST_VEHICLE_MODEL, TEST_VEHICLE_COLOUR)

        then: "it responds with empty optional"
        output == Optional.empty()

        and: "there is log for error"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.get(0).formattedMessage == "Error communicating with Images API: ${errorCode} response for model ${TEST_VEHICLE_MODEL} and colour ${TEST_VEHICLE_COLOUR}"

        where:
        errorCode | _
        400       | _
        404       | _
        500       | _
        503       | _
    }

    //endregion
}
