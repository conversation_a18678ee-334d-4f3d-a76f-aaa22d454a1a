/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider
import com.jaguarlandrover.d9.oneappbackend.springsecurity.token.data.OabUserData
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException

class RegistrationUpdateValidatorSpec extends AbstractSpecification {

    UserDataProvider userDataProvider = Mock()
    RegistrationUpdateValidator registrationUpdateValidator = new RegistrationUpdateValidator(userDataProvider)

    ListAppender logAppender

    def setup() {
        logAppender = setupLogAppender(RegistrationUpdateValidator)
    }

    def "valid if token contains given vehicle Id"() {

        given: "some vehicle id"
        def vehicleId = "some-vehicle-id"

        and: "the token's userdata contains some-vehicle-id in it"
        userDataProvider.getOabUserData() >> OabUserData.builder().vehicles([vehicleId]).build()

        when: "checking if the request is valid"
        def result = registrationUpdateValidator.isValid(vehicleId)

        then: "the output is true"
        result
    }

    def "Exception if request is not valid"() {

        given: "some vehicle id"
        def vehicleId = "some-vehicle-id"

        and: "the token's userdata does not some-vehicle-id in it"
        def someOtherVehicleId = "some-other-vehicle-id"
        userDataProvider.getOabUserData() >> OabUserData.builder().vehicles([someOtherVehicleId]).build()

        when: "checking if the request is valid"
        def result = registrationUpdateValidator.isValid(vehicleId)

        then: "Error is logged"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.get(0).getFormattedMessage().contains("VehicleId provided is not present in the user's FR token entitlement")

        and: "forbidden exception is thrown"
        thrown(ForbiddenException)
    }
}
