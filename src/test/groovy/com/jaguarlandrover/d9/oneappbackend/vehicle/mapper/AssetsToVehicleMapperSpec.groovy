/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.DigitalVehicleDataResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Images
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle

import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.TestHelper.readVehicleData
import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.TestHelper.readVehicleResponse
import static AssetToVehicleImageMapper.L460_PRIMARY_IMAGE
import static AssetToVehicleImageMapper.L461_PRIMARY_IMAGE

class AssetsToVehicleMapperSpec extends AbstractSpecification {

    private static final L461_MODEL_DESCRIPTION = "Range Rover Sport"
    private static final L460_MODEL_DESCRIPTION = "Range Rover"
    private static final X761_MODEL_DESCRIPTION = "X761"

    AssetToVehicleModelDescriptionMapper assetToVehicleModelDescriptionMapper = Mock()
    AssetToVehicleDisplayNameMapper assetToVehicleDisplayNameMapper = Mock()
    AssetToVehicleFuelTypeMapper assetToVehicleFuelTypeMapper = Mock()
    AssetToVehicleColourMapper assetToVehicleColourMapper = Mock()
    AssetToVehicleImageMapper assetToVehicleImageMapper = Mock()

    AssetsToVehicleMapper assetsToVehicleMapper = new AssetsToVehicleMapper(
            assetToVehicleModelDescriptionMapper,
            assetToVehicleDisplayNameMapper,
            assetToVehicleFuelTypeMapper,
            assetToVehicleColourMapper,
            assetToVehicleImageMapper
    )

    def "maps vehicle data to vehicle"() {

        given: "vehicle data object"
        vehicleData

        and: "fuel type is mapped correctly"
        assetToVehicleFuelTypeMapper.map(_) >> Vehicle.FuelType.PHEV

        and: "colour is mapped correctly"
        assetToVehicleColourMapper.map(_) >> colour

        and: "image is mapped correctly"
        assetToVehicleImageMapper.map(_) >> images

        and: "model description is mapped correctly"
        assetToVehicleModelDescriptionMapper.map(_) >> modelDescription

        and: "display name is mapped correctly"
        assetToVehicleDisplayNameMapper.map(_, _) >> displayName

        when: "send to mapper"
        def output = assetsToVehicleMapper.map(vehicleData)

        then: "it is correctly mapped to vehicles"
        output == expectedVehicles

        where:
        vehicleData                                                              | _
        readVehicleData("digitalvehicle/data/vehicleAttributes1vehicleL460.json")         | _
        readVehicleData("digitalvehicle/data/vehicleAttributes1vehicleL461.json")         | _
        readVehicleData("digitalvehicle/data/vehicleAttributes1vehicleX761.json")         | _
        readVehicleData("digitalvehicle/data/vehicleAttributes1vehicleX761NoColour.json") | _
        null                                                                           | _
        VehicleDataResponse.builder().build()                                          | _
        VehicleDataResponse.builder().data(DigitalVehicleDataResponse.builder().assets(null).identity(null).build()).build() | _

        __
        images                                               | modelDescription       | _
        Images.builder().primary(L460_PRIMARY_IMAGE).build() | L460_MODEL_DESCRIPTION | _
        Images.builder().primary(L461_PRIMARY_IMAGE).build() | L461_MODEL_DESCRIPTION | _
        Images.empty()                                       | X761_MODEL_DESCRIPTION | _
        Images.empty()                                       | ""                     | _
        Images.empty()                                       | ""                     | _
        Images.empty()                                       | ""                     | _
        Images.empty()                                       | ""                     | _

        __
        displayName                                          | colour                 | _
        L460_MODEL_DESCRIPTION                               | TEST_VEHICLE_COLOUR    | _
        L461_MODEL_DESCRIPTION                               | TEST_VEHICLE_COLOUR    | _
        X761_MODEL_DESCRIPTION                               | TEST_VEHICLE_COLOUR    | _
        ""                                                   | null                   | _
        ""                                                   | null                   | _
        ""                                                   | null                   | _
        ""                                                   | null                   | _

        __
        expectedVehicles                                                                      | _
        readVehicleResponse("vehicles/mapper/vehicleNoOrdersL460.json")             | _
        readVehicleResponse("vehicles/mapper/vehicleNoOrdersL461.json")             | _
        readVehicleResponse("vehicles/mapper/vehicleNoOrdersNoImage.json")          | _
        readVehicleResponse("vehicles/mapper/vehicleNoOrdersNoImageNoColour.json")  | _
        null                                                                                  | _
        null                                                                                  | _
        null                                                                                  | _
    }

}
