/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.helper

class OabTestConstant {

    static final def SOME_SVCRM_ID = "3333333333"
    static final def SOME_PRINCIPAL_UUID = "*************-2222-2222-************"
    static final def SOME_VEHICLE_IDENTIFIER = "some_vehicle_identifier"
    static final def SOME_OTHER_VEHICLE_ID_VALUE = "some-other-vehicle-id"

    static final def MANAGED_IDENTIFIERS_KEY = "mgd-identifiers"
    static final def MANAGED_ATTRIBUTES_KEY = "mgd-attributes"

    static final def AUTHORIZATION_HEADER = "Authorization"
    static final def BEARER = "Bearer "
    static final def ASSETS = "ASSETS"
    static final def IDENTITY = "IDENTITY"

    static final def EXPIRATION_IN_SECONDS = 120000

    static final def PRIVATE_KEY_FILE_NAME = "/security/private.pem"
    static final def FAKE_KEY_FILE_NAME = "/security/fake_private.pem"

    static final def SUBNAME = "urn:iam2-mgd-v1:mgd-identifiers:oneapp-rangerover:auto-id:principal-uuid:09990x9x-xy99-6969-696x-0xy0900900x0"

    static final def VEHICLE_ID_1 = "c2187a50-5d5e-48b6-bdaa-1bbe6338bf1b"
    static final def VEHICLE_ID_2 = "433e0622-403d-4311-9ba9-9e21a9baa68e"
}
