/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.integration.cn

import com.jaguarlandrover.d9.oneappbackend.vehicle.integration.AbstractSpecificationIT
import com.jaguarlandrover.d9.oneappbackend.vehicle.market.cn.service.CnUserIdentityService
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.IUserIdentityService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext

@SpringBootTest(properties = [
    "service.market=cn"
])
class CnVehicleApplicationSpecIT extends AbstractSpecificationIT {
  @Autowired
  ApplicationContext context

  def "Application starts"() {
    when: "application starts up"

    then: "there are no errors"
    noExceptionThrown()

    and: "CN market services loaded"
    context.getBean(IUserIdentityService).class == CnUserIdentityService
  }
}
