/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.VehicleOrder
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Order
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle
import spock.lang.Specification

class MainMapperSpec extends Specification {

    static def DUPLICATE_VIN = "duplicate-vin"
    static def UNIQUE_VIN_1 = "unique-vin-1"
    static def UNIQUE_VIN_2 = "unique-vin-2"

    OrdersToVehiclesMapper ordersToVehiclesMapper = Mock()
    MainMapper mainMapper = new MainMapper(ordersToVehiclesMapper)

    def "combines attribute vehicles and order vehicles, removing duplicates"() {

        given: "vehicle list with one duplicate vin and one with unique"
        def duplicatedVehicle = Vehicle.builder().vin(DUPLICATE_VIN).order(null).build()
        def uniqueVehicle = Vehicle.builder().vin(UNIQUE_VIN_1).build()
        def vehicleList = [duplicatedVehicle, uniqueVehicle] // vehicle list with duplications

        and: "orders"
        def vehicleOrders = [Mock(VehicleOrder), Mock(VehicleOrder)]

        and: "one is also with duplicate vin and one with unique"
        def duplicatedOrder = Vehicle.builder().vin(DUPLICATE_VIN).order(Mock(Order)).build()
        def uniqueOrder = Vehicle.builder().vin(UNIQUE_VIN_2).build()
        def mappedOrders = [duplicatedOrder, uniqueOrder]

        and: "they can also be mapped"
        ordersToVehiclesMapper.map(vehicleOrders) >> mappedOrders

        when: "send to mapper"
        def output = mainMapper.map(vehicleList, vehicleOrders)

        then: "it is correctly mapped to vehicles, but the duplicated order was removed"
        output as Set == [duplicatedVehicle, uniqueVehicle, uniqueOrder] as Set
    }

    def "combines attribute vehicles and order vehicles with NULL values"() {

        given: "vehicle list of two vehicles with null value for VIN"
        def vehicle1 = Vehicle.builder().vin(null).order(null).build()
        def vehicle2 = Vehicle.builder().vin(null).order(null).build()
        def vehicleList = [vehicle1, vehicle2]

        and: "orders"
        def vehicleOrders = [Mock(VehicleOrder), Mock(VehicleOrder)]

        and: "both also have null VINs"
        def order1 = Vehicle.builder().vin(null).order(Mock(Order)).build()
        def order2 = Vehicle.builder().vin(null).order(Mock(Order)).build()
        def mappedOrders = [order1, order2]

        and: "they can also be mapped"
        ordersToVehiclesMapper.map(vehicleOrders) >> mappedOrders

        when: "send to mapper"
        def output = mainMapper.map(vehicleList, vehicleOrders)

        then: "it is correctly mapped to vehicles, nothing was removed"
        output as Set == [vehicle1, vehicle2, order1, order2] as Set
    }

}
