/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service

import com.jaguarlandrover.d9.oneappbackend.vehicle.client.DigitalVehicleClient
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleRegistrationUpdateRequest
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.RegistrationUpdateRequest
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException
import spock.lang.Specification

class RegistrationUpdateServiceSpec extends Specification {

    DigitalVehicleClient client = Mock()
    RegistrationUpdateValidator registrationUpdateValidator = Mock()
    RegistrationUpdateService service = new RegistrationUpdateService(client, registrationUpdateValidator)

    def "delegates to service updating registration"() {

        given: "a fr token"
        def token = "some token"

        and: "some vehicle id"
        def vehicleId = "some id"

        and: "a registration update request"
        def request = new RegistrationUpdateRequest("some reg")

        and: "the request is valid"
        registrationUpdateValidator.isValid(vehicleId) >> true

        when: "they are sent to the service"
        service.updateVehicleRegistration(token, vehicleId, request)

        then: "client was called to update registration number"
        1 * client.updateVehicleRegistration(
                token,
                vehicleId,
                new VehicleRegistrationUpdateRequest("some reg"))
    }

    def "No updates if request is not valid"() {

        given: "a fr token"
        def token = "some token"

        and: "some vehicle id"
        def vehicleId = "some id"

        and: "a registration update request"
        def request = new RegistrationUpdateRequest("some reg")

        and: "the request is valid"
        def forbiddenException = new ForbiddenException()
        registrationUpdateValidator.isValid(vehicleId) >> {throw forbiddenException}

        when: "they are sent to the service"
        service.updateVehicleRegistration(token, vehicleId, request)

        then: "client is not called to update registration number"
        thrown(ForbiddenException)
        0 * client.updateVehicleRegistration(
                token,
                vehicleId,
                new VehicleRegistrationUpdateRequest("some reg"))
    }
}
