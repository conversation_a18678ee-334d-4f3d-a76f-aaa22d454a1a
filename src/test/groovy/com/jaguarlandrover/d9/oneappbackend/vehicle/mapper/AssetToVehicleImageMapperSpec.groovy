/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Images
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.ImagesService

import static AssetToVehicleImageMapper.*

class AssetToVehicleImageMapperSpec extends AbstractSpecification {

    private static final X761_IMAGE_URL = "https://vehicle-image-url.com/X761.png"
    private static final L460_ORANGE_URL = "https://vehicle-image-url.com/L640_orange.png"
    private static final L461_ORANGE_URL = "https://vehicle-image-url.com/L641_orange.png"
    private static final X761 = "X761"
    private static final ORANGE = "Sanguinello Orange"
    private static final UNKNOWN_MODEL = "XYZ123"
    private static final UNKNOWN_COLOUR = "Some colour"

    ImagesService imagesService = Mock()
    AssetToVehicleColourMapper attributeToVehicleColourMapper = Mock()

    ListAppender logAppender
    AssetToVehicleImageMapper attributeToVehicleImageMapper = new AssetToVehicleImageMapper(imagesService, attributeToVehicleColourMapper)

    def setup() {
        logAppender = setupLogAppender(AssetToVehicleImageMapper)
    }

    def "maps attributes to image correctly"() {
        given: "attributes with model range"
        def attributes = Assets.builder().modelRange(modelRange).build()

        and: "attributes is mapped to colour"
        attributeToVehicleColourMapper.map(attributes) >> colour

        and: "image service returns vehicle image url for given colour"
        imagesService.findVehicleImageUrl(L460, ORANGE) >> Optional.of(L460_ORANGE_URL)
        imagesService.findVehicleImageUrl(L461, ORANGE) >> Optional.of(L461_ORANGE_URL)
        imagesService.findVehicleImageUrl(X761, ORANGE) >> Optional.of(X761_IMAGE_URL)
        imagesService.findVehicleImageUrl(_, _) >> Optional.empty()

        when: "the attributes is sent to the mapper"
        def output = attributeToVehicleImageMapper.map(attributes)

        then: "the image is mapped correctly"
        output == expectedImage

        where:
        modelRange    | colour         | expectedImage
        X761          | ORANGE         | Images.builder().primary(X761_IMAGE_URL).build()
        L460          | ORANGE         | Images.builder().primary(L460_ORANGE_URL).build()
        L461          | ORANGE         | Images.builder().primary(L461_ORANGE_URL).build() 
    }

    def "maps attributes to fallback images if no images found, or empty if no fallback available"() {
        given: "attributes with model range"
        def attributes = Assets.builder().modelRange(modelRange).build()

        and: "attributes is mapped to colour"
        attributeToVehicleColourMapper.map(attributes) >> colour

        and: "image service returns vehicle image url for given colour"
        imagesService.findVehicleImageUrl(L460, ORANGE) >> Optional.of(L460_ORANGE_URL)
        imagesService.findVehicleImageUrl(L461, ORANGE) >> Optional.of(L461_ORANGE_URL)
        imagesService.findVehicleImageUrl(X761, ORANGE) >> Optional.of(X761_IMAGE_URL)
        imagesService.findVehicleImageUrl(_, _) >> Optional.empty()

        when: "the attributes is sent to the mapper"
        def output = attributeToVehicleImageMapper.map(attributes)

        then: "the image is mapped correctly"
        output == expectedImage

        and: "expected logs"
        def logs = filterLogItems(logAppender, Level.WARN)
        expectedLogs.forEach { String expectedLog ->
            assert logs.any { it.formattedMessage.contains(expectedLog) }
        }

        where:
        modelRange    | colour         | expectedImage                                         | _
        // Return fallback images:                                                                 | _
        L460          | null           | Images.builder().primary(L460_PRIMARY_IMAGE).build()  | _
        L461          | null           | Images.builder().primary(L461_PRIMARY_IMAGE).build()  | _
        L460          | UNKNOWN_COLOUR | Images.builder().primary(L460_PRIMARY_IMAGE).build()  | _
        L461          | UNKNOWN_COLOUR | Images.builder().primary(L461_PRIMARY_IMAGE).build()  | _
        // No fallback available:
        null          | ORANGE         | Images.empty()                                        | _
        X761          | null           | Images.empty()                                        | _
        null          | null           | Images.empty()                                        | _
        UNKNOWN_MODEL | ORANGE         | Images.empty()                                        | _
        __
        expectedLogs                                                                                                   | _
        // Return fallback images:
        ["No colour value in attributes", "Responding with colour oblivious image from S3"]                            | _
        ["No colour value in attributes", "Responding with colour oblivious image from S3"]                            | _
        ["No generic image for model: L460 and colour: Some colour", "Responding with colour oblivious image from S3"] | _
        ["No generic image for model: L461 and colour: Some colour", "Responding with colour oblivious image from S3"] | _
        // No fallback available:
        ["No model value in attributes", "Setting null value for image field"]                                         | _
        ["No colour value in attributes", "Setting null value for image field"]                                        | _
        ["No colour value in attributes", "Setting null value for image field"]                                        | _
        ["No generic image for model: XYZ123 and colour: Sanguinello Orange", "Setting null value for image field"]    | _
    }

}
