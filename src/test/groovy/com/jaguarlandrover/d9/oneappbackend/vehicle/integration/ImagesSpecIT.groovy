/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.integration

import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabFrTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabTestConstant
import com.jaguarlandrover.d9.oneappbackend.vehicle.repository.VehicleImages
import org.skyscreamer.jsonassert.JSONAssert

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get as mockMvcGet
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class ImagesSpecIT extends AbstractSpecificationIT {

    def "Returns image url from DynamoDB for vehicle orders"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle configured"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 404, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi("/orders/vehicleOrdersResponse1order.json")

        and: "repository contains image for vehicle order"
        def vehicleImages = VehicleImages.builder().orderId(TEST_ORDER_NUMBER).topViewImageUrl(TEST_VEHICLE_IMAGE_URL).build()
        assert repository.save(vehicleImages)

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/noVehicle1orderWithImage.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)
    }

    def "Pulls top image from images API and saves in DynamoDB"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle configured"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 404, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi("/orders/vehicleOrdersResponse1order.json")

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/topImagePresent.json", 200)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/noVehicle1orderImageFromClient.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)

        and: "image is saved in the repository"
        repository.findById(TEST_ORDER_NUMBER).isPresent()

        and: "correct image was saved"
        repository.findById(TEST_ORDER_NUMBER).get().getTopViewImageUrl() == "https://iglo-vehicleimage.pre-prod.jlr-ddc.com/17751078/png/Ext/Top_L.png"
    }

    def "No image returned if top image not present both in DynamoDB and in the response from image service"() {

        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle configured"
        configureDigitalVehicleApi("/digitalvehicle/error/someErrorFromDVResponse.json", forgeRockToken, 404, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is configured"
        configureOrdersApi("/orders/vehicleOrdersResponse1order.json")

        and: "images API is configured"
        configureImagesApiForOrderImage("/images/missingTopImage.json", 200)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/noVehicle1orderNoImage.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)
    }

    def "Pulls generic image for vehicle colour and model"() {
        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/data/vehicleAttributes1vehicleL461.json", forgeRockToken, 200, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is responding with 404"
        // configureOrdersApi() <- commenting this out will give us 404

        and: "images API is configured"
        configureImagesApiForGenericImage("/images/genericImage.json", 200)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/vehicleNoOrders.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)
    }

    def "Responds with colour oblivious image from S3 for models L460 and L461 if no URL or no colour"() {
        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/data/vehicleAttributes1vehicleL461.json", forgeRockToken, 200, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is responding with 404"
        // configureOrdersApi() <- commenting this out will give us 404

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/vehicleNoOrdersL461.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)

        where:
        digitalVehicleApiResponseJson                                     | expectedResponseJson
        "/digitalvehicle/data/vehicleAttributes1vehicleL461.json"         | "/vehicles/response/vehicleNoOrdersL461.json"
        "/digitalvehicle/data/vehicleAttributes1vehicleL461NoColour.json" | "/vehicles/response/vehicleNoOrdersL461NoColour.json"
    }

    def "Responds with null for other models when that generic image not found"() {
        given: "forge rock token containing user details and vehicle id"
        def forgeRockToken = OabFrTokenTestHelper.happyScenarioToken()

        and: "ForgeRock server is configured"
        configureForgeRockApi()

        and: "digital vehicle api is configured"
        configureDigitalVehicleApi("/digitalvehicle/data/vehicleAttributes1vehicleX761.json", forgeRockToken, 200, List.of(OabTestConstant.ASSETS, OabTestConstant.IDENTITY))

        and: "vehicle orders api is responding with 404"
        // configureOrdersApi() <- commenting this out will give us 404

        and: "generic images API is configured"
        configureImagesApiForEmptyGenericImageResponse(204)

        and: "repository doesn't have any images"
        repository.deleteAll()

        when: "Vehicle Controller endpoint is called"
        def response = mvc.perform(mockMvcGet("/api/v1/users/me/vehicles")
                .header("Authorization", "Bearer " + forgeRockToken))

        then: "The response is OK"
        response.andExpect(status().isOk())

        and: "The response body as expected"
        def expectedResponseBody = this.getClass().getResource("/vehicles/response/vehicleNoOrdersNoImage.json").text
        JSONAssert.assertEquals(expectedResponseBody, response.andReturn().response.contentAsString, false)

        where:
        digitalVehicleApiResponseJson                                     | expectedResponseJson
        "/digitalvehicle/data/vehicleAttributes1vehicleX761.json"         | "/vehicles/response/vehicleNoOrdersNoImage.json"
        "/digitalvehicle/data/vehicleAttributes1vehicleX761NoColour.json" | "/vehicles/response/vehicleNoOrdersNoImageNoColour.json"
    }

}
