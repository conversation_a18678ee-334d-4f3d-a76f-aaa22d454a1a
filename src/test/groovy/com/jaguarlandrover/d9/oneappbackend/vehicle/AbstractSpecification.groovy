/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import spock.lang.Specification

import static org.slf4j.LoggerFactory.getLogger

class AbstractSpecification extends Specification {

    static final def TEST_ORDER_NUMBER = "10001111"
    static final def TEST_ORDER_NUMBER_2 = "10002222"
    static final def TEST_VEHICLE_COLOUR = "Sanguinello Orange"
    static final def TEST_VEHICLE_COLOUR_2 = "Zadar Grey"
    static final def TEST_VEHICLE_MODEL = "L461"

    static final def TEST_VEHICLE_IMAGE_URL = "https://jlr.com/myImaginaryImage/10001111"

    static ListAppender<ILoggingEvent> setupLogAppender(Class<?>... classNames) {
        def listAppender = new ListAppender<ILoggingEvent>()
        listAppender.start()

        classNames.each { className ->
            def logger = (Logger) getLogger(className)
            logger.addAppender(listAppender)
        }
        return listAppender
    }

    def filterLogItems(ListAppender<ILoggingEvent> listAppender, Level level) {
        listAppender.list.findAll() { it.getLevel() == level }
    }
}
