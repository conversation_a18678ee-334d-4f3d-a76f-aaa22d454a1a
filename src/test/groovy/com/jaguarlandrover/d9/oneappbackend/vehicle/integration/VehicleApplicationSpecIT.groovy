/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.integration

import com.jaguarlandrover.d9.oneappbackend.vehicle.market.uk.service.UserIdentityService
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.IUserIdentityService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext

class VehicleApplicationSpecIT extends AbstractSpecificationIT {
  @Autowired
  ApplicationContext context

  def "Application starts"() {
    when: "application starts up"

    then: "there are no errors"
    noExceptionThrown()

    and: "UK market services loaded"
    context.getBean(IUserIdentityService).class == UserIdentityService
  }
}
