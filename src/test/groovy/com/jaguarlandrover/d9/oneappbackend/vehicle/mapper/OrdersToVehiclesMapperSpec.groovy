/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Images
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.TestHelper

class OrdersToVehiclesMapperSpec extends AbstractSpecification {

    OrderToVehicleImageMapper orderToVehicleImageMapper = Mock()
    OrdersToVehiclesMapper vehicleOrdersToVehiclesMapper = new OrdersToVehiclesMapper(orderToVehicleImageMapper)

    def "maps vehicle orders to vehicles"() {

        given: "vehicle orders"
        def vehicleOrders = TestHelper.readVehicleOrder(vehicleOrdersJson)

        and: "images is mapped correctly for a given order number"
        orderToVehicleImageMapper.map(_ as String) >> Images.builder().primary(TEST_VEHICLE_IMAGE_URL).build()

        when: "send to mapper"
        def output = vehicleOrdersToVehiclesMapper.map(vehicleOrders)

        then: "it is correctly mapped to vehicles"
        output == TestHelper.readVehicleOrdersResponse(expectedOutputJson)

        where:
        vehicleOrdersJson                                              | _
        "orders/vehicleOrdersResponse1order.json"                      | _
        "orders/vehicleOrdersResponse1orderNullFields.json"            | _
        "orders/vehicleOrdersResponse2orders.json"                     | _
        "orders/vehicleOrdersResponse1orderNoPlannedDeliveryDate.json" | _
        __
        expectedOutputJson                                                              | _
        "vehicles/response/noVehicle1orderWithImage.json"                               | _
        "vehicles/response/noVehicle1orderNullResponse.json"                            | _
        "vehicles/response/noVehicle2ordersWithImage.json"                              | _
        "vehicles/response/noVehicle1orderWithImageNoEstimatedPlannedDeliveryDate.json" | _
    }

}
