/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.controller

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.RegistrationUpdateRequest
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.RegistrationUpdateService
import spock.lang.Specification

class RegistrationUpdateControllerSpec extends Specification {

    RegistrationUpdateService registrationUpdateService = Mock()
    RegistrationUpdateController controller = new RegistrationUpdateController(registrationUpdateService)


    def "delegates to service updating registration"() {

        given: "a fr token"
        def token = "some token"

        and: "some vehicle id"
        def vehicleId = "some id"

        and: "a registration update request"
        def request = new RegistrationUpdateRequest("some reg")

        when: "it is sent to the controller"
        def output = controller.updateVehicleRegistration(token, vehicleId, request)

        then: "OK status is returned"
        output.statusCode.value() == 200

        and: "service was called to update registration number"
        1 * registrationUpdateService.updateVehicleRegistration(token, vehicleId, request)

    }
}
