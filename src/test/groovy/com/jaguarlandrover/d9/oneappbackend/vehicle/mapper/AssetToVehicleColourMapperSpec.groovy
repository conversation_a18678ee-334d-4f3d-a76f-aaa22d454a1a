/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Feature

import static AssetToVehicleColourMapper.COLOUR_EFG_TYPE_NAME

class AssetToVehicleColourMapperSpec extends AbstractSpecification {

    private static final String SOMETHING_ELSE = "SOMETHING_ELSE"
    private static final String JAGUAR_BRAND_NAME = "JAG"
    private static final String LANDROVER_BRAND_NAME = "LandRover"

    AssetToVehicleColourMapper attributeToVehicleColourMapper = new AssetToVehicleColourMapper()

    def "maps attributes to colour correctly"() {
        given: "attributes with features"
        def attributes = Assets.builder().features(features).brand(brand)build()

        when: "the attributes is sent to the mapper"
        def output = attributeToVehicleColourMapper.map(attributes)

        then: "the colour is mapped correctly"
        output == expectedColour

        where:
        features                                                                                                     | _
        [Feature.builder().efgTypeName(COLOUR_EFG_TYPE_NAME).brandDescriptionLandRover(TEST_VEHICLE_COLOUR).build()] | _
        [Feature.builder().efgTypeName(COLOUR_EFG_TYPE_NAME).brandDescriptionJaguar(TEST_VEHICLE_COLOUR_2).build()]  | _
        [Feature.builder().efgTypeName(COLOUR_EFG_TYPE_NAME).brandDescriptionJaguar(TEST_VEHICLE_COLOUR_2).build()]  | _
        [Feature.builder().efgTypeName(COLOUR_EFG_TYPE_NAME).brandDescriptionLandRover(null).build()]                | _
        [Feature.builder().efgTypeName(SOMETHING_ELSE).brandDescriptionLandRover(TEST_VEHICLE_COLOUR).build()]       | _
        [Feature.builder().efgTypeName(null).brandDescriptionLandRover(TEST_VEHICLE_COLOUR).build()]                 | _
        null                                                                                                         | _
        __
        brand                 |  expectedColour        | _
        LANDROVER_BRAND_NAME  |  TEST_VEHICLE_COLOUR   | _
        JAGUAR_BRAND_NAME     |  TEST_VEHICLE_COLOUR_2 | _
        null                  |  null                  | _
        LANDROVER_BRAND_NAME  |  null                  | _
        LANDROVER_BRAND_NAME  |  null                  | _
        LANDROVER_BRAND_NAME  |  null                  | _
        LANDROVER_BRAND_NAME  |  null                  | _
    }

}
