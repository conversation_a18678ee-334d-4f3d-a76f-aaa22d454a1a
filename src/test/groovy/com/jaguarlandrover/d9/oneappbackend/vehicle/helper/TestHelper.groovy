/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.helper

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.VehicleOrder
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle

class TestHelper {

    static def readVehicleOrder(String sourceFile) {
        readJsonFile(sourceFile, new TypeReference<List<VehicleOrder>>(){})
    }

    static def readVehicleOrdersResponse(String sourceFile) {
        readJsonFile(sourceFile, new TypeReference<List<Vehicle>>(){})
    }

    static def readVehicleResponse(String sourceFile) {
        readJsonFile(sourceFile, Vehicle.class)
    }

    static def readVehicleData(String sourceFile) {
        readJsonFile(sourceFile, VehicleDataResponse.class)
    }

    static <T> T readJsonFile(String sourceFile, TypeReference<T> classRef) {
        def response = ClassLoader.getSystemClassLoader().getResource(sourceFile).text

        new ObjectMapper().registerModule(new JavaTimeModule())
                .readValue(response, classRef)
    }

    static <T> T readJsonFile(String sourceFile, Class<T> classRef) {
        def response = ClassLoader.getSystemClassLoader().getResource(sourceFile).text

        new ObjectMapper().registerModule(new JavaTimeModule())
                .readValue(response, classRef)
    }
}
