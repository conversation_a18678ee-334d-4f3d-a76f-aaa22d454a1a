/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.controller

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.RemoveVehicleService
import org.springframework.http.HttpStatus

class RemoveVehicleControllerSpec extends AbstractSpecification {

    private final String TOKEN = "token_sample"
    private final String VEHICLE_ID = "vehicle_id"

    RemoveVehicleService removeVehicleService = Mock()

    RemoveVehicleController controller
    ListAppender logAppender

    def setup() {
        controller = new RemoveVehicleController(removeVehicleService)
        logAppender = setupLogAppender(RemoveVehicleController)
    }

    def "Controller responds with successful log"() {

        when: "Vehicle Controller endpoint is called "
        def response = controller.removeVehicle(TOKEN, VEHICLE_ID)

        then: "The return response status is OK"
        response.statusCode == HttpStatus.OK

        and: "Removing vehicle success was logged"
        def logs = filterLogItems(logAppender, Level.INFO)
        logs.size() == 2
        logs.get(1).getFormattedMessage().contains("Successfully removed vehicle with ID " + VEHICLE_ID)
    }
}
