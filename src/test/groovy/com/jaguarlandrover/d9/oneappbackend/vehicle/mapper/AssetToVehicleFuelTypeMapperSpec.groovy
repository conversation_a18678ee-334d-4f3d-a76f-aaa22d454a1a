/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle

class AssetToVehicleFuelTypeMapperSpec extends AbstractSpecification {

    AssetToVehicleFuelTypeMapper attributeToVehicleFuelTypeMapper = new AssetToVehicleFuelTypeMapper()

    def "maps attributes to fuel type correctly"() {
        given: "attributes with fuel type"
        def attributes = Assets.builder().fuelType(fuelType).build()

        when: "the attributes is sent to the mapper"
        def output = attributeToVehicleFuelTypeMapper.map(attributes)

        then: "the fuel type is mapped correctly"
        output == expectedFuelType

        where:
        fuelType                                             | expectedFuelType
        [Assets.FuelType.HYBRID, Assets.FuelType.PETROL]     | Vehicle.FuelType.PHEV
        [Assets.FuelType.MHEV, Assets.FuelType.DIESEL]       | Vehicle.FuelType.ICE
        [Assets.FuelType.DIESEL]                             | Vehicle.FuelType.ICE
        [Assets.FuelType.PETROL]                             | Vehicle.FuelType.ICE
        [Assets.FuelType.PHEV]                               | Vehicle.FuelType.PHEV
        [Assets.FuelType.HEV]                                | Vehicle.FuelType.PHEV
        [Assets.FuelType.MHEV]                               | Vehicle.FuelType.ICE
        [Assets.FuelType.MHEV, Assets.FuelType.PETROL]       | Vehicle.FuelType.ICE
        [Assets.FuelType.BEV]                                | Vehicle.FuelType.BEV
        [Assets.FuelType.PHEV, Assets.FuelType.HEV]          | Vehicle.FuelType.PHEV
        [Assets.FuelType.UNKNOWN]                            | Vehicle.FuelType.UNKNOWN
        null                                                 | Vehicle.FuelType.UNKNOWN
    }

}
