/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Images
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.TestHelper
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.ImagesService

class OrderToVehicleImageMapperSpec extends AbstractSpecification {

    ImagesService imagesService = Mock()
    OrderToVehicleImageMapper orderToVehicleImageMapper = new OrderToVehicleImageMapper(imagesService)

    def "maps images url correctly if imageService returns url"() {
        given: "image can be found for a given order number"
        imagesService.findVehicleImageUrl(TEST_ORDER_NUMBER) >> Optional.of(TEST_VEHICLE_IMAGE_URL)

        when: "test order is send to mapper"
        def output = orderToVehicleImageMapper.map(TEST_ORDER_NUMBER)

        then: "the url is mapped correctly"
        output == Images.builder().primary(TEST_VEHICLE_IMAGE_URL).build()
    }

    def "maps to Images with null fields if imageService returns empty"() {
        given: "image cannot be found for a given order number"
        imagesService.findVehicleImageUrl(TEST_ORDER_NUMBER) >> Optional.empty()

        when: "test order is send to mapper"
        def output = orderToVehicleImageMapper.map(TEST_ORDER_NUMBER)

        then: "it is mapped to Images with null fields"
        output == Images.builder().build()
    }

}
