/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.market.cn.service

import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider
import com.jaguarlandrover.d9.oneappbackend.springsecurity.token.data.OabUserData
import spock.lang.Specification

class CnUserIdentityServiceSpec extends Specification {
  UserDataProvider userDataProvider = Mock()
  def identityService = new CnUserIdentityService(userDataProvider)

  def "get user id should return cdt id"() {
    given: "assume user data exists"
    userDataProvider.getOabUserData() >> new OabUserData(svcrmId: "svcrmId-1", customerCdtId: "cdtId-1")

    when: "get user id"
    def id = identityService.getUserId()

    then: "should return cdt id"
    id == "cdtId-1"
  }
}
