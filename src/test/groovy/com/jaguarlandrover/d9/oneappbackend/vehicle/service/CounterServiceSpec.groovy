/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service

import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry

import static com.jaguarlandrover.d9.oneappbackend.vehicle.service.CounterService.*

class CounterServiceSpec extends AbstractSpecification {

    Counter requestCounter = Mock()
    Counter errorCounter = Mock()
    Counter noVehiclesInTokenCounter = Mock()
    Counter vehiclesRetrievedCounter = Mock()

    MeterRegistry meterRegistry = Mock()
    CounterService counterService

    def setup() {
        meterRegistry.counter("vehicle.requests.received") >> requestCounter
        meterRegistry.counter("vehicle.requests.errors") >> errorCounter

        meterRegistry.counter(OAB_VEHICLE_REQUESTS_RECEIVED) >> requestCounter
        meterRegistry.counter(OAB_VEHICLE_REQUESTS_ERRORS) >> errorCounter
        meterRegistry.counter(OAB_VEHICLE_REQUEST_NO_VEHICLES_IN_TOKEN) >> noVehiclesInTokenCounter
        meterRegistry.counter(OAB_VEHICLES_RETRIEVED) >> vehiclesRetrievedCounter

        counterService = new CounterService(meterRegistry)
    }

    def "provides request counter"() {

        when: "counter requested"
        def output = counterService.getRequestCounter()

        then: "it is provided"
        output != null
    }

    def "provides error counter"() {

        when: "counter requested"
        def output = counterService.getErrorCounter()

        then: "it is provided"
        output != null
    }

    def "provides no vehicles counter"() {

        when: "counter requested"
        def output = counterService.getNoVehiclesInTokenCounter()

        then: "it is provided"
        output != null
    }

    def "provides vehicles retrieved counter"() {

        when: "counter requested"
        def output = counterService.getVehiclesRetrievedCounter()

        then: "it is provided"
        output != null
    }
}
