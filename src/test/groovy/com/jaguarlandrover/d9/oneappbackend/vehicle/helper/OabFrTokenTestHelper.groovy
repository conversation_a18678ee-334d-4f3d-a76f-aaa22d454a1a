/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.helper

import io.jsonwebtoken.Jwts
import lombok.SneakyThrows

import java.security.KeyFactory
import java.security.PrivateKey
import java.security.cert.CertificateFactory
import java.security.spec.PKCS8EncodedKeySpec

import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OabTestConstant.*

class OabFrTokenTestHelper {

    static final def OAB_FR_AUDIENCE = "oneapp-rangerover"
    static final def CERTIFICATE_TYPE = "X.509"
    static final def CERTIFICATE = "MIIDPzCCAicCFDaux5Nqp7A3u9lAggNdBGiRjJgKMA0GCSqGSIb3DQEBCwUAMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTAeFw0yMzAyMjExMTQwMzRaFw0yNDAyMjExMTQwMzRaMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMenrDCTh5O5FO8aLZxyW0zaflUttzdM/ji0sTpNBbiYnFFvC+Dw+oeIAEHV213AsgvwNgF5EZ/MGFANSFDBWKejNuHOhcATI2nUSKpKQCgI6h9glwYJSxTuDfKhRwdfs9szmfMxed5qdi1B9pc3Mdkc4MwLd7COv93QKn7pZ/kCi0/A5xaEKHjSd7lGEF0a9sfqQ3xVyZYg+8Nlf/wfPn0qZ1+WrG+VEVuU87siawk2NOlVbACa0ycZf0HB7Fr2zz0b0fO8drVsjqJIzbH5KDa6PvFuOg9LWfJzUtLl5AARCVhLDnTX+qXRcX8oYYQ2QTbLL6CDc4be8SSLYNIHeGcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAUNff3yk/5PBdScIVsUbqI2oSCluGrsSXUmC5KST+rXN+NE38p3a5RUTTQ+Srl57my5RspdP/i+Ylz5zIcQWBtcGxH3RAT8SjRJ6sLA5PCLaeQaLQvEOqZ++1ImSP9BhFyouQtV7zHWjWNUep8tqykDHdCXNLkvQy7BFnxaVvRVMO0sLRlHnB+NNOVEk/XCOili17f8Yqa+fdtpROXn3hkd4iWJAKgXYPnfuFiilj4lJz4uw+ePB3u7EIR4XQjU96SM2xmSaV3lzAMtlZDrIadskEpSK9bdkJC/8ftJmdFEAmOd+fAegVofvr/tdyWKCbsHN9DJXgvNTKl/GOLxfGWQ=="
    static final def ISSUER_BASE_URL = "http://localhost:8083"
    static final def ISSUER = ISSUER_BASE_URL + "/gateway/oauth2/realms/root/realms/customer"

    static def baseToken() {
        Jwts.builder()
                .setIssuedAt(new Date())
                .setExpiration(new Date(new Date().getTime() + EXPIRATION_IN_SECONDS))
                .signWith(privateKey())
                .setSubject(SUBNAME)
                .setAudience(OAB_FR_AUDIENCE)
                .setIssuedAt(new Date())
                .setIssuer(ISSUER)
                .setExpiration(new Date(new Date().getTime() + EXPIRATION_IN_SECONDS))
                .claim(MANAGED_IDENTIFIERS_KEY, managedIdentifiersPrincipalIdAndSvcrmId())
                .claim(MANAGED_ATTRIBUTES_KEY, managedAttributesRegisteredVehicle())
    }

    static def twoVehiclesToken() {
        Jwts.builder()
                .setIssuedAt(new Date())
                .setExpiration(new Date(new Date().getTime() + EXPIRATION_IN_SECONDS))
                .signWith(privateKey())
                .setSubject(SUBNAME)
                .setAudience(OAB_FR_AUDIENCE)
                .setIssuedAt(new Date())
                .setIssuer(ISSUER)
                .setExpiration(new Date(new Date().getTime() + EXPIRATION_IN_SECONDS))
                .claim(MANAGED_IDENTIFIERS_KEY, managedIdentifiersPrincipalIdAndSvcrmId())
                .claim(MANAGED_ATTRIBUTES_KEY, managedAttributesRegisteredVehicles(VEHICLE_ID_1, VEHICLE_ID_2))
    }


    static def happyScenarioToken() {
        BEARER + baseToken().compact()
    }

    static def tokenPrincipalIdOnly() {
        BEARER + baseToken()
                .claim(MANAGED_IDENTIFIERS_KEY, managedIdentifiersPrincipalIdOnly())
                .compact()
    }

    static def tokenEmptyIdentities() {
        BEARER + baseToken()
                .claim(MANAGED_IDENTIFIERS_KEY, [])
                .compact()
    }

    static def tokenNoIdentities() {
        BEARER + baseToken()
                .claim(MANAGED_IDENTIFIERS_KEY, null)
                .compact()
    }

    static def tokenEmptyAttributes() {
        BEARER + baseToken()
                .claim(MANAGED_ATTRIBUTES_KEY, [])
                .compact()
    }

    static def tokenNoAttributes() {
        BEARER + baseToken()
                .claim(MANAGED_ATTRIBUTES_KEY, null)
                .compact()
    }

    static def tokenTwoAttributes() {
        BEARER + twoVehiclesToken().compact()
    }

    static def tokenNoIdentitiesNoAttributes() {
        BEARER + baseToken()
                .claim(MANAGED_IDENTIFIERS_KEY, null)
                .claim(MANAGED_ATTRIBUTES_KEY, null)
                .compact()
    }

    static def noExpiryToken() {
        BEARER + baseToken()
                .setExpiration(null)
                .compact()
    }

    static def expiredToken() {
        BEARER + baseToken()
                .setExpiration(new Date(new Date().getTime() - 1))
                .compact()
    }

    static def noSubjectToken() {
        BEARER + baseToken()
                .setSubject(null)
                .compact()
    }

    static def noIssuerToken() {
        BEARER + baseToken()
                .setIssuer(null)
                .compact()
    }

    static def wrongIssuerToken() {
        BEARER + baseToken()
                .setIssuer("some_issuer")
                .compact()
    }

    static def noAudienceToken() {
        BEARER + baseToken()
                .setAudience(null)
                .compact()
    }

    static def wrongAudienceToken() {
        BEARER + baseToken()
                .setAudience("some_audience")
                .compact()
    }

    static def someRandomToken() {
        "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    }

    // region Identifiers

    static def managedIdentifiersPrincipalIdAndSvcrmId() {
        [
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SAP-CDC:UID:some_uid",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SVCRM:crmId:" + SOME_SVCRM_ID,
                "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:" + SOME_PRINCIPAL_UUID
        ]
    }

    static def managedIdentifiersMoreIds_1() {
        [
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SAP-CDC:UID:********************************",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:*************-2222-2222-************",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SVCRM:crmId:3333333333:primary",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SFCRM:crmId:444444444444444444:secondary",
        ]
    }


    static def managedIdentifiersMoreIds_2() {
        [
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SFCRM:crmId:444444444444444444:secondary",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SVCRM:crmId:3333333333:primary",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SAP-CDC:UID:********************************",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:*************-2222-2222-************",
        ]
    }

    static def managedIdentifiersPrincipalIdOnly() {
        [
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SAP-CDC:UID:some_uid",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:" + SOME_PRINCIPAL_UUID
        ]
    }

    static def managedIdentifiersSvcrmIdOnly() {
        [
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SAP-CDC:UID:some_uid",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SVCRM:crmId:" + SOME_SVCRM_ID
        ]
    }

    static def managedIdentifiersNoRequiredIds() {
        [
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SFCRM:crmId:444444444444444444:secondary",
                "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SAP-CDC:UID:********************************"
        ]
    }

    //endregion

    //region Attributes

    static def managedAttributesRegisteredVehicle(def vehicleIdentifier = SOME_VEHICLE_IDENTIFIER) {
        [
                "urn:iam2-mgd-v1:mgd-attributes:vehicle:vehicle-identity:iam2-consumer-rights-v1:digital-vehicle:vehicle-id:" + vehicleIdentifier + ":primary"
        ]
    }

    static def managedAttributesRegisteredVehicles(def vehicleIdentifier1, def vehicleIdentifier2) {
        [
                "urn:iam2-mgd-v1:mgd-attributes:vehicle:vehicle-identity:iam2-consumer-rights-v1:digital-vehicle:vehicle-id:" + vehicleIdentifier1 + ":primary",
                "urn:iam2-mgd-v1:mgd-attributes:vehicle:vehicle-identity:iam2-consumer-rights-v1:digital-vehicle:vehicle-id:" + vehicleIdentifier2 + ":primary"
        ]
    }

    static def managedAttributesNoVehicles() {
        [
                "urn:iam2-mgd-v1:mgd-attributes:sales:vehicle-orders:eapim-api-rights-v1:vehicle-orders-service:search.query",
                "urn:iam2-mgd-v1:mgd-attributes:sales:vehicle-orders:eapim-api-rights-v1:vehicle-orders-service:orders.read"
        ]
    }

    static def managedMixedAttributes() {
        [
                "urn:iam2-mgd-v1:mgd-attributes:vehicle:vehicle-identity:iam2-consumer-rights-v1:digital-vehicle:vehicle-id:VEHICLE_ONE:primary",
                "urn:iam2-mgd-v1:mgd-attributes:sales:vehicle-orders:eapim-api-rights-v1:vehicle-orders-service:search.query",
                "urn:iam2-mgd-v1:mgd-attributes:vehicle:vehicle-identity:iam2-consumer-rights-v1:digital-vehicle:vehicle-id:VEHICLE_TWO:primary",
                "urn:iam2-mgd-v1:mgd-attributes:sales:vehicle-orders:eapim-api-rights-v1:vehicle-orders-service:orders.read"
        ]
    }

    //endregion

    //region keys

    @SneakyThrows
    static def privateKey() {

        String privateKeyStr = OabFrTokenTestHelper.getResource(PRIVATE_KEY_FILE_NAME).text

        privateKeyStr = privateKeyStr.replace("-----BEGIN PRIVATE KEY-----", "")
        privateKeyStr = privateKeyStr.replace("-----END PRIVATE KEY-----", "")
        privateKeyStr = privateKeyStr.replaceAll("\n", "")

        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyStr))
        KeyFactory kf = KeyFactory.getInstance("RSA")
        PrivateKey privateKey = kf.generatePrivate(keySpec)

        privateKey
    }

    @SneakyThrows
    static def publicKey() {

        byte[] decodedCertificate = Base64.getDecoder().decode(CERTIFICATE)

        CertificateFactory.getInstance(CERTIFICATE_TYPE)
                .generateCertificate(new ByteArrayInputStream(decodedCertificate))
                .getPublicKey()
    }

    //endregion
}
