/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.exception

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import io.jsonwebtoken.ExpiredJwtException
import io.jsonwebtoken.PrematureJwtException
import org.springframework.core.MethodParameter
import org.springframework.http.HttpStatus
import org.springframework.web.bind.MissingRequestHeaderException

import java.lang.reflect.Method

class GlobalExceptionHandlerSpec extends AbstractSpecification {

    GlobalExceptionHandler globalExceptionHandler = new GlobalExceptionHandler()

    ListAppender logAppender

    def setup() {
        logAppender = setupLogAppender(GlobalExceptionHandler)
    }

    def "Handles correctly any exception"() {

        when: "Exception handler handles account not found exception"
        def output = globalExceptionHandler.handleRequestError(new RuntimeException())

        then: "The response is 500"
        output.statusCode == HttpStatus.INTERNAL_SERVER_ERROR

        and: "the stack trace of the exception is printed"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.first().getMessage().contains("RuntimeException")
    }

    def "Handles authorisation exception correctly"() {

        when: "Exception handler handles account not found exception"
        def output = globalExceptionHandler.handleRequestError(
                new AuthorisationException(new RuntimeException()))

        then: "The response is 401"
        output.statusCode == HttpStatus.UNAUTHORIZED

        and: "the stack trace of the exception is printed"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.first().getMessage().contains("RuntimeException")
    }

    def "Handles PrematureJwtException correctly"() {

        when: "Exception handler handles account not found exception"
        def output = globalExceptionHandler.handleRequestError(Mock(PrematureJwtException))

        then: "The response is 401"
        output.statusCode == HttpStatus.UNAUTHORIZED

        and: "the stack trace of the exception is printed"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
    }

    def "Handles ExpiredJwtException correctly"() {

        when: "Exception handler handles account not found exception"
        def output = globalExceptionHandler.handleRequestError(Mock(ExpiredJwtException))

        then: "The response is 401"
        output.statusCode == HttpStatus.UNAUTHORIZED

        and: "the stack trace of the exception is printed"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
    }

    def "Handles forbidden exception correctly"() {

        when: "Exception handler handles forbidden response"
        def output = globalExceptionHandler.handleRequestError(
                new ForbiddenException())

        then: "The response is 403"
        output.statusCode == HttpStatus.FORBIDDEN
        output.body.message == "User is not authorized to access this resource with an explicit deny"

        and: "the stack trace of the exception is printed"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.first().getMessage().contains("RuntimeException")
    }

    def "Handles missing header exception correctly"() {

        when: "Exception handler handles missing header exception exception"
        def output = globalExceptionHandler.handleRequestError(
                new MissingRequestHeaderException("Authorization", getParameter()))

        then: "The response is 401"
        output.statusCode == HttpStatus.UNAUTHORIZED

        and: "the stack trace of the exception is printed"
        def logs = filterLogItems(logAppender, Level.ERROR)
        logs.size() == 1
        logs.first().getMessage().contains("MissingRequestHeaderException")
    }

    def testMethod(String authHeader) {
        return authHeader
    }

    MethodParameter getParameter() {
        Method testMethod = Arrays.stream(getClass().getMethods())
                .filter(m -> m.getName().equals("testMethod"))
                .findFirst()
                .orElseThrow(IllegalStateException::new)
        return new MethodParameter(testMethod, 0)
    }
}
