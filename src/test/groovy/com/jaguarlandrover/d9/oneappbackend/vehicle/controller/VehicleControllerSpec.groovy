/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.controller


import com.jaguarlandrover.d9.oneappbackend.vehicle.service.CounterService
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.VehicleService
import io.micrometer.core.instrument.Counter
import org.springframework.http.HttpStatus
import spock.lang.Specification

class VehicleControllerSpec extends Specification {

    private final String TOKEN = "token_sample"

    VehicleService vehicleService = Mock()
    CounterService counterService = Mock()

    Counter requestCounter = Mock()
    Counter errorCounter = Mock()

    VehicleDetailsController controller

    def setup() {
        counterService.getRequestCounter() >> requestCounter
        counterService.getErrorCounter() >> errorCounter
        controller = new VehicleDetailsController(vehicleService, counterService)
    }

    def "Happy path: controller responds with vehicleDetails for given token"() {

        given: "Service respond with vins list from vehicle details"
        def vinsList = []
        vehicleService.getVehicleDetails(TOKEN) >> vinsList

        when: "Vehicle Controller endpoint is called "
        def response = controller.getVehicleDetails(TOKEN)

        then: "The return response status is OK"
        response.statusCode == HttpStatus.OK

        and: "The response body is as expected"
        response.body == vinsList

        and: "required metrics are updated"
        1 * requestCounter.increment()
    }
}
