/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service

import ch.qos.logback.classic.Level
import ch.qos.logback.core.read.ListAppender
import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider
import com.jaguarlandrover.d9.oneappbackend.springsecurity.token.data.OabUserData
import com.jaguarlandrover.d9.oneappbackend.vehicle.AbstractSpecification
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.WirelessCarGlueWrapperClient
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleArchitectureType
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.BadGatewayException

class RemoveVehicleServiceSpec extends AbstractSpecification {

    def static final TOKEN = "some-token"
    def static final VEHICLE_ID_1 = "some-vehicle-id-1"
    def static final VEHICLE_ID_2 = "some-vehicle-id-2"

    ListAppender logAppender

    VehicleArchitectureService vehicleArchitectureService = Mock()
    UserDataProvider userDataProvider = Mock()
    WirelessCarGlueWrapperClient wirelessCarGlueWrapperClient = Mock()
    RemoveVehicleService removeVehicleService = new RemoveVehicleService(vehicleArchitectureService,
            userDataProvider, wirelessCarGlueWrapperClient)

    def setup() {
        logAppender = setupLogAppender(RemoveVehicleService)
    }

    def "Service responds with success"() {

        given: "we have valid vehicle id for user"
        def userData = Mock(OabUserData)
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "we know vehicle architecture"
        def vehicleArhitecture = VehicleArchitectureType.EVA_2

        and: "vehicle architecture service responds correctly for given vehicle ID"
        vehicleArchitectureService.getVehicleArchitecture(TOKEN, VEHICLE_ID_1) >> vehicleArhitecture

        when: "service is asked to remove vehicle"
        removeVehicleService.removeVehicle(TOKEN, VEHICLE_ID_1)

        then: "no exception was thrown"
        def warnLogs = filterLogItems(logAppender, Level.WARN)
        warnLogs.size() == 0

        and: "no errors is logged"
        def errorLogs = filterLogItems(logAppender, Level.ERROR)
        errorLogs.size() == 0
    }

    def "Service responds with forbidden exception if no vehicle id in the token"() {

        given: "we have some vehicle id for user"
        def userData = Mock(OabUserData)
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "we know vehicle architecture"
        def vehicleArchitecture = VehicleArchitectureType.EVA_2

        and: "vehicle architecture service responds correctly for given vehicle ID"
        vehicleArchitectureService.getVehicleArchitecture(TOKEN, VEHICLE_ID_1) >> vehicleArchitecture

        when: "service is asked to remove vehicle with another id"
        removeVehicleService.removeVehicle(TOKEN, VEHICLE_ID_2)

        then: "exception was thrown"
        thrown(ForbiddenException)
    }

    def "Service responds with 502 error if WirelessCar Wrapper API fails"() {

        given: "we have some vehicle id for user"
        def userData = Mock(OabUserData)
        userData.getVehicles() >> [VEHICLE_ID_1]
        userDataProvider.getOabUserData() >> userData

        and: "we know vehicle architecture"
        def vehicleArchitecture = VehicleArchitectureType.EVA_2

        and: "vehicle architecture service responds correctly for given vehicle ID"
        vehicleArchitectureService.getVehicleArchitecture(TOKEN, VEHICLE_ID_1) >> vehicleArchitecture

        and: "wireless car glue wrapper returns error"
        def wcException = new Exception("WirelessCar Glue Wrapper API error") // 404, 401,
        wirelessCarGlueWrapperClient.removeVehicleById(TOKEN, VEHICLE_ID_1) >> {throw wcException}

        when: "service is asked to remove vehicle"
        removeVehicleService.removeVehicle(TOKEN, VEHICLE_ID_1)

        then: "exception was thrown"
        thrown(BadGatewayException)

    }
}
