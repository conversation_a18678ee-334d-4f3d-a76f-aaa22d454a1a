<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <!-- Log messages in JSON format -->
    <springProfile name="test">
        <appender name="Console"
                  class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
        </appender>

        <!-- LOG everything at INFO level -->
        <root level="info">
            <appender-ref ref="Console"/>
        </root>
    </springProfile>

    <!-- Log messages in plain text -->
    <springProfile name="test">
        <appender name="Console"
                  class="ch.qos.logback.core.ConsoleAppender">
            <layout class="ch.qos.logback.classic.PatternLayout">
                <Pattern>
                    %d{ISO8601} %highlight(%-5level) [%blue(%t)] %yellow(%C): %msg%n%throwable %green(x-amzn-RequestId=%X{requestId}){}%n
                </Pattern>
            </layout>
        </appender>

        <!-- LOG everything at INFO level -->
        <root level="info">
            <appender-ref ref="Console"/>
        </root>
    </springProfile>

</configuration>
