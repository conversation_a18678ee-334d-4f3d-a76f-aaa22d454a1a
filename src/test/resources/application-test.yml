server:
  port: 8080

spring:
  application:
    name: oab-vehicle-service

logging:
  level:
    com.jaguarlandrover.d9.oneappbackend.vehicle: DEBUG

#
# Defined by our application
#
attribute-service:


ecosys-order-service:
  base-url: http://localhost:8082

forge-rock:
  base-url: http://localhost:8083

ecosys-image-service:
  base-url: http://localhost:8084

ecosys-tsdp-wrapper-service:
  base-url: http://localhost:8086

digital-vehicle:
  base-url: http://localhost:8085

app:
  config:
    modelDescription:
      L460: Range Rover
      L461: Range Rover Sport

order:
  config:
    responseFlag: true

#
# Defined for oab security library
#
oab:
  security:
    forge-rock:
      base-url: http://localhost:8083
