server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: oab-vehicle-service
  cloud:
    aws:
      region:
        static: eu-west-2
        auto: false

logging:
  level:
    com.jaguarlandrover.d9.oneappbackend.vehicle: DEBUG

#
# Defined by our application
#
app:
  config:
    modelDescription:
      L460: Range Rover
      L461: Range Rover Sport

order:
  config:
    responseFlag: true
