/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.market.uk.service;

import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider;
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.IUserIdentityService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserIdentityService implements IUserIdentityService {
  private final UserDataProvider userDataProvider;

  @Override
  public String getUserId() {
    return userDataProvider.getOabUserData().getSvcrmId();
  }
}
