/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.exception;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.PrematureJwtException;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;


@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

  @ExceptionHandler(AuthorisationException.class)
  public ResponseEntity<Error> handleRequestError(AuthorisationException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
  }

  @ExceptionHandler(PrematureJwtException.class)
  public ResponseEntity<Error> handleRequestError(PrematureJwtException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
  }

  @ExceptionHandler(ExpiredJwtException.class)
  public ResponseEntity<Error> handleRequestError(ExpiredJwtException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
  }

  @ExceptionHandler(ForbiddenException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(ForbiddenException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    var error = RestErrorResponse.builder()
        .message(exception.getMessage())
        .build();
    return ResponseEntity.status(HttpStatus.FORBIDDEN).body(error);
  }

  @ExceptionHandler(MissingRequestHeaderException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(MissingRequestHeaderException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    var error = RestErrorResponse.builder()
        .message(exception.getMessage())
        .build();
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(error);
  }

  @ExceptionHandler(BadGatewayException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(BadGatewayException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    var error = RestErrorResponse.builder()
        .message(exception.getMessage())
        .build();
    return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(error);
  }

  @ExceptionHandler(NotFoundException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(NotFoundException exception) {
    var error = RestErrorResponse.builder()
        .message(exception.getMessage())
        .build();
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
  }

  @ExceptionHandler(RemoteServiceException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(RemoteServiceException exception) {
    var error = RestErrorResponse.builder()
        .message(exception.getMessage())
        .build();
    return ResponseEntity.status(HttpStatus.valueOf(exception.getStatusCode())).body(error);
  }

  @ExceptionHandler(DigitalVehicleException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(DigitalVehicleException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    var error = RestErrorResponse.builder()
        .message(exception.getMessage())
        .build();
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
  }

  @ExceptionHandler(UnknownArchitectureException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(UnknownArchitectureException exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    var error = RestErrorResponse.builder()
        .message(exception.getMessage())
        .build();
    return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(error);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<RestErrorResponse> handleRequestError(MethodArgumentNotValidException ex) {
    List<ErrorDetails> errorDetails = ex.getBindingResult().getAllErrors()
        .stream().map(valError -> new ErrorDetails(
            valError.getCode(),
            ((FieldError) valError).getField(),
            valError.getDefaultMessage()
        )).toList();
    var error = RestErrorResponse.builder()
        .message("Validation failed")
        .errors(errorDetails)
        .build();
    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<Error> handleRequestError(Exception exception) {
    log.error(ExceptionUtils.getStackTrace(exception));
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
  }

  @Builder
  @Getter
  @Setter
  @AllArgsConstructor
  public static class RestErrorResponse {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer code;

    private String message;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ErrorDetails> errors;
  }

  @Getter
  @Setter
  @AllArgsConstructor
  private static class ErrorDetails {
    private String type;
    private String field;
    private String description;
  }
}
