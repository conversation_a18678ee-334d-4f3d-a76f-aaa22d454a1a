/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.controller;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.RegistrationUpdateRequest;
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.RegistrationUpdateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping(value = "/api/v1", produces = MediaType.APPLICATION_JSON_VALUE)
public class RegistrationUpdateController {

  private final RegistrationUpdateService registrationUpdateService;

  public RegistrationUpdateController(RegistrationUpdateService registrationUpdateService) {
    this.registrationUpdateService = registrationUpdateService;
  }

  /**
   * Update registration number of given vehicle.
   * @param token ForgeRock JWT Access Token
   * @param vehicleId vehicle ID
   * @param request registration to be updated
   * @return REST response with 200 status code
   */
  @Tag(name = "Update Vehicle Registration")
  @Operation(summary = "Update registration number of given vehicle",
      description = "Given a ForgeRock Access Token, update registration number of given vehicle.")
  @ApiResponses(value = {
      @ApiResponse(
          responseCode = "200",
          description = "Registration number updated.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "401",
          description = "Unauthorized to access.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "403",
          description = "Access to vehicles is forbidden with the given access token.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "500",
          description = "Internal Server Error.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "502",
          description = "Failed to connect to upstream server.",
          content = @Content(schema = @Schema()))
  })
  //@Timed(value = "oab.vehicle.request.update.registration.measured")
  @PatchMapping(value = "/users/me/vehicles/{vehicleId}")
  public ResponseEntity<Void> updateVehicleRegistration(
      @RequestHeader(name = "Authorization") String token,
      @PathVariable String vehicleId,
      @Valid @RequestBody RegistrationUpdateRequest request) {
    log.info("Received request to update registration number for vehicle ID {}", vehicleId);
    registrationUpdateService.updateVehicleRegistration(token, vehicleId, request);
    return ResponseEntity.status(200).build();
  }

}
