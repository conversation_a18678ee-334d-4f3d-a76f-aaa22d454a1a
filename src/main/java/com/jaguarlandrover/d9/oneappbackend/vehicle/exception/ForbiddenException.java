/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.exception;

public class ForbiddenException extends RuntimeException {

  public static final String MESSAGE = "User is not authorized to access this resource with an explicit deny";

  public ForbiddenException() {
    super(MESSAGE, new RuntimeException());
  }

  public ForbiddenException(String message) {
    super(message);
  }

  public ForbiddenException(Exception ex) {
    super(MESSAGE, ex);
  }

}
