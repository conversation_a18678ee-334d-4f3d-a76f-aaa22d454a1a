/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShippingAttributes {

  @JsonProperty("planned_vessel_name")
  private String plannedVesselName;

  @JsonProperty("actual_vessel_name")
  private String actualVesselName;

  @JsonProperty("planned_sail_date")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
  private LocalDateTime plannedSailDate;

  @JsonProperty("shipping_bill_of_landing_id")
  private Integer shippingBillOfLandingId;

  @JsonProperty("shipping_bill_of_landing_date")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
  private LocalDateTime shippingBillOfLandingDate;

  @JsonProperty("shipping_bill_of_landing_status")
  private String shippingBillOfLadingStatus;

  @JsonProperty("intermediate_destination_id")
  private String intermediateDestinationId;

  @JsonProperty("planned_intermediate_destination_date")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
  private LocalDateTime plannedIntermediateDestinationDate;

  @JsonProperty("intermediate_destination_date")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
  private LocalDateTime intermediateDestinationDate;

  @JsonProperty("final_destination_id")
  private String finalDestinationId;

  @JsonProperty("final_destination_description")
  private String finalDestinationDescription;

  @JsonProperty("final_destination_org_level")
  private Integer finalDestinationOrgLevel;
}
