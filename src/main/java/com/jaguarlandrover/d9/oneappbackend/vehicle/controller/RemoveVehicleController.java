/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.controller;


import com.jaguarlandrover.d9.oneappbackend.vehicle.service.RemoveVehicleService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1", produces = MediaType.APPLICATION_JSON_VALUE)
public class RemoveVehicleController {
  private final RemoveVehicleService removeVehicleService;

  public RemoveVehicleController(RemoveVehicleService removeVehicleService) {
    this.removeVehicleService = removeVehicleService;
  }

  /**
   * Removes/unbind given vehicle.
   * @param token ForgeRock JWT Access Token
   * @param vehicleId vehicle ID
   * @return REST response with 200 status code
   */
  @Tag(name = "Remove Vehicle")
  @Operation(summary = "Remove vehicle",
      description = "Remove vehicle by vehicle ID.")
  @ApiResponses(value = {
      @ApiResponse(
          responseCode = "200",
          description = "Vehicle removed.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "401",
          description = "Unauthorized to access.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "403",
          description = "Access to vehicles is forbidden with the given access token.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "404",
          description = "Not Found.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "500",
          description = "Internal Server Error.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "502",
          description = "Failed to connect to upstream server.",
          content = @Content(schema = @Schema()))
  })
  @Timed(value = "oab.vehicle.request.delete.measured")
  @DeleteMapping(value = "/users/me/vehicles/{vehicleId}")
    public ResponseEntity<Void> removeVehicle(
        @RequestHeader(name = "Authorization") String token,
        @PathVariable String vehicleId) {
    log.info("Received request to remove vehicle with ID {}", vehicleId);
    removeVehicleService.removeVehicle(token, vehicleId);
    log.info("Successfully removed vehicle with ID {}", vehicleId);
    return ResponseEntity.status(200).build();
  }
}
