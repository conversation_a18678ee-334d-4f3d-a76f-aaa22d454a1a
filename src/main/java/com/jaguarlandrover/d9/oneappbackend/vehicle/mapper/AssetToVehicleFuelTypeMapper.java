/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle;
import java.util.Arrays;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class AssetToVehicleFuelTypeMapper {

  // List of PHEV Fuel Types from Vehicle Attributes API Gateway
  private static final List<Assets.FuelType> PHEV_LIST = Arrays.asList(
      Assets.FuelType.HYBRID,
      Assets.FuelType.PHEV,
      Assets.FuelType.HEV);
  // List of ICE Fuel Types from Vehicle Attributes API Gateway
  private static final List<Assets.FuelType> ICE_LIST = Arrays.asList(
      Assets.FuelType.MHEV,
      Assets.FuelType.DIESEL,
      Assets.FuelType.PETROL);

  public Vehicle.FuelType map(Assets assets) {
    List<Assets.FuelType> fuelTypes = assets.getFuelType();
    if (fuelTypes != null) {
      if (fuelTypes.stream().anyMatch(PHEV_LIST::contains)) {
        return Vehicle.FuelType.PHEV;
      } else if (fuelTypes.stream().anyMatch(ICE_LIST::contains)) {
        return Vehicle.FuelType.ICE;
      } else if (fuelTypes.contains(Assets.FuelType.BEV)) {
        return Vehicle.FuelType.BEV;
      }
    }

    return Vehicle.FuelType.UNKNOWN;
  }

}
