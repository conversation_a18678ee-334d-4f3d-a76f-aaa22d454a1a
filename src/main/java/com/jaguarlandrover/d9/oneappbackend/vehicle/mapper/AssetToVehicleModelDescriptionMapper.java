/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.config.VehicleProperties;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class AssetToVehicleModelDescriptionMapper {

  private final Map<String, String> modelDescriptionMap;

  public AssetToVehicleModelDescriptionMapper(VehicleProperties vehicleProperties) {
    this.modelDescriptionMap = vehicleProperties.getModelDescription();
  }

  public String map(Assets assets) {
    if (modelDescriptionMap != null
        && modelDescriptionMap.containsKey(assets.getModelRange())) {
      return modelDescriptionMap.get(assets.getModelRange());
    }
    return assets.getModelRange();
  }

}
