/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Assets {

  public enum FuelType {
    DIESEL,
    PETROL,
    BEV,    // Battery Electric Vehicle
    HEV,    // Hybrid Electric Vehicle
    PHEV,   // Plugin Hybrid Electric Vehicle
    MHEV,   // Mild Hybrid Electric Vehicle
    HYBRID,
    UNKNOWN
  }

  private String vehicleArchitecture;
  private String fleetId;
  private String brand;
  private String modelRange;
  private String targetMarket;
  private String soldIntoMarket;
  private String modelYear;
  private String modelName;
  private String trim;
  private String bodyStyle;
  private String driver;
  private String transmission;
  private String engine;
  private String plant;
  private List<FuelType> fuelType;
  private List<Feature> features;
}
