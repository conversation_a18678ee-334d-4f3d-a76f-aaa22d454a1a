/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.bootstrap;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(
    name = "service.market",
    havingValue = "uk",
    matchIfMissing = true
)
// Scan all packages for UK.
@ComponentScan(value = {
    "com.jaguarlandrover.d9.oneappbackend.vehicle.market.uk"
})
public class VehicleServiceConfiguration {
}
