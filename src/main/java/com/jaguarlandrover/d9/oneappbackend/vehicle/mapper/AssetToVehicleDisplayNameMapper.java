/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Identity;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class AssetToVehicleDisplayNameMapper {

  private final AssetToVehicleModelDescriptionMapper attributeToVehicleModelDescriptionMapper;

  public AssetToVehicleDisplayNameMapper(
      AssetToVehicleModelDescriptionMapper attributeToVehicleModelDescriptionMapper) {
    this.attributeToVehicleModelDescriptionMapper = attributeToVehicleModelDescriptionMapper;
  }

  public String map(Assets assets, Identity identity) {
    return getFilteredDisplayName(getAttributesToCheck(assets, identity));
  }

  private List<String> getAttributesToCheck(Assets assets, Identity identity) {
    return Arrays.asList(identity.getVehicleReg(), attributeToVehicleModelDescriptionMapper.map(assets),
        assets.getModelRange(), identity.getUniqueId());
  }

  private String getFilteredDisplayName(List<String> attributesToDisplayNameValue) {
    return attributesToDisplayNameValue
        .stream()
        .filter(StringUtils::isNotBlank)
        .findFirst()
        .orElse(null);
  }

}
