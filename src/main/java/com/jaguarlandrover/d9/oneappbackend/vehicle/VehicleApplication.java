/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;

@OpenAPIDefinition(info = @Info(
    version = "3.0.1",
    title = "Vehicle Service API",
    description = "Microservice for vehicle operations",
    license = @License(name = "Copyright (c) 2023. Jaguar Land Rover - All Rights Reserved."),
    contact = @Contact(name = "One App Backend Team", email = "<EMAIL>")))
@SpringBootApplication(scanBasePackages = {
        "com.jaguarlandrover.d9.oneappbackend.vehicle.bootstrap",
        "com.jaguarlandrover.d9.oneappbackend.vehicle.config",
        "com.jaguarlandrover.d9.oneappbackend.vehicle.controller",
        "com.jaguarlandrover.d9.oneappbackend.vehicle.exception",
        "com.jaguarlandrover.d9.oneappbackend.vehicle.mapper",
        "com.jaguarlandrover.d9.oneappbackend.vehicle.service",
        "com.jaguarlandrover.d9.oneappbackend.vehicle.repository"
    },
    exclude = { UserDetailsServiceAutoConfiguration.class })
@EnableFeignClients(basePackages = {
    "com.jaguarlandrover.d9.oneappbackend.springsecurity.client",
    "com.jaguarlandrover.d9.oneappbackend.vehicle.client"
})
@ImportAutoConfiguration({FeignAutoConfiguration.class})
public class VehicleApplication {

  public static void main(String[] args) {
    SpringApplication.run(VehicleApplication.class, args);
  }
}
