/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service;

import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.ExceptionHandler.handleFeignException;

import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider;
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.WirelessCarGlueWrapperClient;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleArchitectureType;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.BadGatewayException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException;
import feign.FeignException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RemoveVehicleService {

  private final VehicleArchitectureService vehicleArchitectureService;
  private final UserDataProvider userDataProvider;
  private final WirelessCarGlueWrapperClient wirelessCarGlueWrapperClient;

  public RemoveVehicleService(VehicleArchitectureService vehicleArchitectureService,
                              UserDataProvider userDataProvider,
                              WirelessCarGlueWrapperClient wirelessCarGlueWrapperClient) {
    this.vehicleArchitectureService = vehicleArchitectureService;
    this.userDataProvider = userDataProvider;
    this.wirelessCarGlueWrapperClient = wirelessCarGlueWrapperClient;
  }

  /**
  * Remove Vehicle from VCDP or Wireless Car.
  * @param token ForgeRock JWT access token
  * @param vehicleId vehicle ID
  */
  public void removeVehicle(String token, String vehicleId) {

    List<String> vehicles = userDataProvider.getOabUserData().getVehicles();
    if (!vehicles.contains(vehicleId)) {
      String errorMessage = String.format("User cannot remove vehicle ID: %s", vehicleId);
      throw new ForbiddenException(errorMessage);
    }

    log.info("Getting vehicle architecture from Digital Vehicle API");

    VehicleArchitectureType architectureType = vehicleArchitectureService.getVehicleArchitecture(token, vehicleId);

    switch (architectureType) {
      case VehicleArchitectureType.EVA_2:
        log.info("Vehicle architecture is EVA_2, call Wireless Car Glue wrapper service to remove vehicle");
        callWirelessCarGlueWrapper(token, vehicleId);
        break;
      case VehicleArchitectureType.EVA_25:
        log.info("Vehicle architecture is EVA_25, this vehicle is not supported yet");
        break;
      default:
        log.info("Unknown vehicle architecture: {}, not supported.", architectureType);
        break;
    }
  }

  private void callWirelessCarGlueWrapper(String token, String vehicleId) {
    try {
      wirelessCarGlueWrapperClient.removeVehicleById(token, vehicleId);
    } catch (FeignException ex) {
      log.error("WirelessCar Glue Wrapper API error: {}", ex.getMessage());
      handleFeignException("WirelessCar Glue Wrapper API", ex);
    } catch (Exception ex) {
      log.error("Something unpredicted happened with Wireless Car Glue Wrapper Service", ex);
      throw new BadGatewayException("WirelessCar Glue Wrapper API");
    }
  }
}
