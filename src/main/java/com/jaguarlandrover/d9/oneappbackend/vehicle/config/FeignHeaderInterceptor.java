/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.config;

import static com.jaguarlandrover.d9.oneappbackend.vehicle.config.OabLogInterceptor.REQUEST_ID_HEADER_NAME;
import static com.jaguarlandrover.d9.oneappbackend.vehicle.config.OabLogInterceptor.REQUEST_ID_PROPERTY_NAME;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.stereotype.Component;

@Component
public class FeignHeaderInterceptor implements RequestInterceptor {

  @Override
  public void apply(RequestTemplate template) {

    String requestId = ThreadContext.get(REQUEST_ID_PROPERTY_NAME);
    if (StringUtils.isNotBlank(requestId)) {
      template.header(REQUEST_ID_HEADER_NAME, requestId);
    }
  }
}