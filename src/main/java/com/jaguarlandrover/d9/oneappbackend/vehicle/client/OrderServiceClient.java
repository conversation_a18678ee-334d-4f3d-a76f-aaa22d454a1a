/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.client;

import com.jaguarlandrover.d9.oneappbackend.vehicle.config.FeignOkHttpConfiguration;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.VehicleOrder;
import io.micrometer.core.annotation.Timed;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(value = "ecosys-order-service-client",
    url = "${ecosys-order-service.base-url}",
    configuration = FeignOkHttpConfiguration.class)
public interface OrderServiceClient {

  @Timed(value = "oab.vehicle.order.request.measured")
  @GetMapping(value = "/api/v1/users/{userId}/orders")
  List<VehicleOrder> getAllOrdersByUserId(@RequestHeader("Authorization") String token,
                                          @PathVariable("userId") String userId);

}
