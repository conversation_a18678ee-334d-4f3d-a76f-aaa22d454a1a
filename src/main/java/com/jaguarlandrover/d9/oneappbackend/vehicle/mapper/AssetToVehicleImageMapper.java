/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Images;
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.ImagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AssetToVehicleImageMapper {

  public static final String L460_PRIMARY_IMAGE =
      "https://app-default-images-vcslwtdekydzosljwlyg-eu-west-2.s3.eu-west-2.amazonaws"
          + ".com/L460_24MY.png";
  public static final String L461_PRIMARY_IMAGE =
      "https://app-default-images-vcslwtdekydzosljwlyg-eu-west-2.s3.eu-west-2.amazonaws"
          + ".com/L461_24MY.png";
  public static final String L460 = "L460";
  public static final String L461 = "L461";

  private final ImagesService imagesService;
  private final AssetToVehicleColourMapper attributeToVehicleColourMapper;

  public AssetToVehicleImageMapper(ImagesService imagesService,
                                   AssetToVehicleColourMapper attributeToVehicleColourMapper) {
    this.imagesService = imagesService;
    this.attributeToVehicleColourMapper = attributeToVehicleColourMapper;
  }

  public Images map(Assets assets) {

    String model = assets.getModelRange();
    String colour = attributeToVehicleColourMapper.map(assets);

    if (colour == null) {
      log.warn("No colour value in attributes");
      return colourObliviousOrEmptyImage(assets);
    }

    if (model == null) {
      log.warn("No model value in attributes");
      return colourObliviousOrEmptyImage(assets);
    }

    String url = imagesService.findVehicleImageUrl(model, colour).orElse(null);

    if (url == null) {
      log.warn("No generic image for model: {} and colour: {}", model, colour);
      return colourObliviousOrEmptyImage(assets);
    }

    log.info("Image for model: {} and colour: {} found at url: {}",
        model,
        colour,
        url);

    return Images.builder().primary(url).build();
  }

  private Images colourObliviousOrEmptyImage(Assets assets) {
    if (L460.equals(assets.getModelRange())) {
      log.warn("Responding with colour oblivious image from S3 for L460");
      return Images.builder().primary(L460_PRIMARY_IMAGE).build();
    } else if (L461.equals(assets.getModelRange())) {
      log.warn("Responding with colour oblivious image from S3 for L431");
      return Images.builder().primary(L461_PRIMARY_IMAGE).build();
    }
    log.warn("Setting null value for image field");
    return Images.empty();
  }

}
