/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.market.cn.service;

import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider;
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.IUserIdentityService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CnUserIdentityService implements IUserIdentityService {
  private final UserDataProvider userDataProvider;

  @Override
  public String getUserId() {
    return userDataProvider.getOabUserData().getCustomerCdtId();
  }
}
