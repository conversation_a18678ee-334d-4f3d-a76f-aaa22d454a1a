/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.client;

import com.jaguarlandrover.d9.oneappbackend.vehicle.config.FeignOkHttpConfiguration;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleRegistrationUpdateRequest;
import io.micrometer.core.annotation.Timed;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "digital-vehicle-client",
    url = "${digital-vehicle.base-url}",
    configuration = FeignOkHttpConfiguration.class)
public interface DigitalVehicleClient {

  @Timed(value = "oab.vehicle.digitalvehicle.one.vehicle.details.get.measured")
  @GetMapping(value = "/vehicles/{vehicleId}")
  VehicleDataResponse getVehicleData(
      @RequestHeader(value = "Authorization") String accessToken,
      @PathVariable String vehicleId,
      @RequestParam String include);

  @Timed(value = "oab.vehicle.digitalvehicle.registration.patch.measured")
  @PatchMapping("/vehicles/{vehicleId}/identity/reg-number")
  ResponseEntity<Void> updateVehicleRegistration(
      @RequestHeader(value = "Authorization") String accessToken,
      @PathVariable String vehicleId,
      @Valid @RequestBody VehicleRegistrationUpdateRequest request);
}
