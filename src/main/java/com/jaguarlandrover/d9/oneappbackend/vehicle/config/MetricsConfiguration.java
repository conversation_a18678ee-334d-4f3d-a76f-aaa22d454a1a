/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.config;

import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import java.util.List;
import java.util.Map;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(MetricsProperties.class)
public class MetricsConfiguration {

  @Bean
  public TimedAspect timedAspect(MeterRegistry registry) {
    return new TimedAspect(registry);
  }

  @Bean
  public MeterRegistry getMeterRegistry() {
    return new CompositeMeterRegistry();
  }

  @Bean
  public MeterFilter commonTagsMeterFilter(MetricsProperties properties) {
    return MeterFilter.commonTags(createCommonTags(properties.getTags()));
  }

  private List<Tag> createCommonTags(Map<String, String> tags) {
    return tags.entrySet().stream()
        .map(entry -> Tag.of(entry.getKey(), entry.getValue()))
        .toList();
  }

}
