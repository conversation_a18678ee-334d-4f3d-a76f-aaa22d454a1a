/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleArchitectureType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Vehicle {

  public enum FuelType {
    ICE,    // Internal Combustion Engine
    BEV,    // Battery Electric Vehicle
    PHEV,   // Plugin Hybrid Electric Vehicle
    UNKNOWN
  }

  @Schema(example = "78af3491-5c12-7ad3-d3d4-02ba17294dfc")
  private String vid;

  @Schema(example = "SALYL2FV0KA225520")
  private String vin;

  @Schema(example = "EVA_2")
  private VehicleArchitectureType architecture;

  @Schema(example = "JAG")
  private String brand;

  @Schema(example = "Defender")
  private String model;

  @Schema
  @JsonProperty("model_name")
  private String modelName;

  @Schema(example = "MY21")
  @JsonProperty("model_year")
  private String modelYear;

  @Schema(example = "Defender 90 D240 (2.0 Diesel SD4 240HP) AWD Auto First Edition")
  private String description;

  @Schema(example = "Pangea Green")
  private String colour;

  @Schema(example = "Baby Defender")
  private String nickname;

  @Schema(example = "OWNED")
  @JsonProperty("ownership_type")
  private String ownershipType;

  @Schema(example = "MA22 ABC")
  private String registration;

  @Schema(example = "PHEV")
  @JsonProperty("fuel_type")
  private FuelType fuelType;

  private Images images;
  private Order order;

  @Schema(example = "Range Rover Sport")
  @JsonProperty("display_name")
  private String displayName;
}
