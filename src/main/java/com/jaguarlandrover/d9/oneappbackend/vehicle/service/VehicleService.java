/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service;

import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.IncludeConstants.ASSETS;
import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.IncludeConstants.IDENTITY;

import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider;
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.DigitalVehicleClient;
import com.jaguarlandrover.d9.oneappbackend.vehicle.client.OrderServiceClient;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.VehicleOrder;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.AuthorisationException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.BadGatewayException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.mapper.AssetsToVehicleMapper;
import com.jaguarlandrover.d9.oneappbackend.vehicle.mapper.MainMapper;
import feign.FeignException;
import feign.RetryableException;
import io.micrometer.core.annotation.Timed;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class VehicleService {

  private static final int UNAUTHORIZED = 401;
  private static final int FORBIDDEN = 403;

  private final UserDataProvider userDataProvider;
  private final IUserIdentityService userIdentityService;
  private final DigitalVehicleClient digitalVehicleClient;
  private final OrderServiceClient orderServiceClient;
  private final boolean orderResponseFlag;
  private final MainMapper mainMapper;
  private final CounterService counterService;
  private final AssetsToVehicleMapper assetsToVehicleMapper;

  public VehicleService(UserDataProvider userDataProvider,
                        IUserIdentityService userIdentityService,
                        DigitalVehicleClient digitalVehicleClient,
                        OrderServiceClient orderServiceClient,
                        @Value("${order.config.responseFlag:false}") boolean orderResponseFlag,
                        MainMapper mainMapper,
                        CounterService counterService, AssetsToVehicleMapper assetsToVehicleMapper) {
    this.userDataProvider = userDataProvider;
    this.userIdentityService = userIdentityService;
    this.digitalVehicleClient = digitalVehicleClient;
    this.orderServiceClient = orderServiceClient;
    this.orderResponseFlag = orderResponseFlag;
    this.mainMapper = mainMapper;
    this.counterService = counterService;
    this.assetsToVehicleMapper = assetsToVehicleMapper;
  }

  public List<Vehicle> getVehicleDetails(String token) {
    String userId = userIdentityService.getUserId();
    Optional<String> userIdOptional = Optional.ofNullable(userId);

    userIdOptional.ifPresentOrElse(
        s -> log.info("User ID extracted from token: {}", s),
        () -> log.info("No User ID in token")
    );

    List<String> tokenAttributes = getTokenAttributes(userIdOptional);

    List<Vehicle> vehicles = getAllVehicleDetails(token, tokenAttributes);

    countRetrievedVehicles(tokenAttributes);

    if (vehicles.isEmpty() && !tokenAttributes.isEmpty()) {
      log.error("No vehicles were found for user {} with token vehicle attributes: {}",
          userIdOptional.orElse(""), tokenAttributes);
    }

    List<VehicleOrder> vehicleOrders = getVehicleOrders(token, userIdOptional);

    return mainMapper.map(vehicles, vehicleOrders);
  }

  @Timed(value = "oab.vehicle.digitalvehicle.all.vehicle.details.get.measured")
  private List<Vehicle> getAllVehicleDetails(String token, List<String> tokenAttributes) {
    return tokenAttributes.stream()
        .map(vehicleId -> fetchAndMapVehicle(token, vehicleId))
        .filter(Objects::nonNull)
        .toList();
  }

  private List<String> getTokenAttributes(Optional<String> userIdOptional) {
    List<String> vehicles = userDataProvider.getOabUserData().getVehicles();
    if (vehicles == null || vehicles.isEmpty()) {
      counterService.getNoVehiclesInTokenCounter().increment();
      log.warn("Missing vehicles in token for user {}", userIdOptional.orElse(""));
      return Collections.emptyList();
    } else {
      log.info("Vehicle ids in token: {}", vehicles);
    }
    return vehicles;
  }

  private Vehicle fetchAndMapVehicle(String token, String vehicleId) {
    String includeParam = String.join(",", List.of(ASSETS, IDENTITY));

    try {
      log.info("Calling Digital Vehicle for vehicle data");
      VehicleDataResponse vehicleDataResponse = digitalVehicleClient.getVehicleData(
          token,
          vehicleId,
          includeParam);
      log.info("Finished getting vehicle data");
      return assetsToVehicleMapper.map(vehicleDataResponse);
    } catch (FeignException exception) {
      counterService.getErrorCounter().increment();
      log.error("Exception when calling Digital Vehicle API", exception);
      dealWithFeignException(exception);
    } catch (Exception ex) {
      counterService.getErrorCounter().increment();
      log.error("Something unpredicted happened", ex);
    }
    return null;
  }

  private void countRetrievedVehicles(List<String> vehicleIds) {
    if (!vehicleIds.isEmpty()) {
      counterService.getVehiclesRetrievedCounter().increment(vehicleIds.size());
    }
  }

  private List<VehicleOrder> getVehicleOrders(String token, Optional<String> userIdOptional) {
    List<VehicleOrder> vehicleOrders = new ArrayList<>();
    if (orderResponseFlag && userIdOptional.isPresent()) {
      log.info("order response flag enabled and user id exists");
      try {
        log.info("Started getting Orders by user Id");
        vehicleOrders = orderServiceClient.getAllOrdersByUserId(token, userIdOptional.get());
        log.info("Finished getting Orders by user Id");
      } catch (FeignException exception) {
        if (exception.status() == HttpStatus.NOT_FOUND.value()) {
          // Do nothing if no order is found for this customer
          log.info("Order not found for this user");
        } else {
          // Failed to connect to order service.
          log.error("Exception when calling vehicle orders API", exception);
        }
      }
    }
    return vehicleOrders;
  }

  private static void dealWithFeignException(FeignException exception) {
    if (exception.status() == FORBIDDEN) {
      throw new ForbiddenException(exception);
    } else if (exception.status() == UNAUTHORIZED) {
      throw new AuthorisationException(exception);
    } else if (exception instanceof RetryableException) {
      log.error("Error calling Digital Vehicle API: {}", exception.getMessage(), exception);
      throw new BadGatewayException("Digital Vehicle API");
    }
  }
}
