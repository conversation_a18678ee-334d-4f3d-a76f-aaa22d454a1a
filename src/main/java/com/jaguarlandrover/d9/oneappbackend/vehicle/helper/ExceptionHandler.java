/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.helper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.AuthorisationException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.BadGatewayException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.NotFoundException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.RemoteServiceException;
import feign.FeignException;
import feign.RetryableException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ExceptionHandler {
  private static final int UNAUTHORIZED = 401;
  private static final int FORBIDDEN = 403;
  private static final int NOT_FOUND = 404;
  private static final int BAD_GATEWAY = 502;
  private static final String VEHICLE_NOT_FOUND_MSG = "Vehicle not found.";
  private static final String DEFAULT_MSG = "Error while calling remote service.";
  private static final String FORMAT = "%s: %s";

  private ExceptionHandler() {
  }

  /**
   * Handles FeignException thrown by calls to remote API
   * and throws an appropriate custom exception based on HTTP status codes.
   *
   * @param serviceName name of the remote service being called
   * @param exception   FeignException thrown by the Feign client
   *
   * @throws AuthorisationException  if status is 401
   * @throws ForbiddenException      if status is 403
   * @throws NotFoundException       if status is 404
   * @throws BadGatewayException     if status is 502 or the exception is retryable
   * @throws RemoteServiceException  for any other error
   */
  public static void handleFeignException(String serviceName, FeignException exception) {
    int status = exception.status();

    String extractedMessage = extractErrorMessageFromFeign(serviceName, exception);

    log.error("Failed calling remote API: {}, with status {}: {}", serviceName, status, extractedMessage);

    if (status == UNAUTHORIZED) {
      throw new AuthorisationException(extractedMessage);
    } else if (status == FORBIDDEN) {
      throw new ForbiddenException(extractedMessage);
    } else if (status == NOT_FOUND) {
      throw new NotFoundException(extractedMessage);
    } else if (status == BAD_GATEWAY || exception instanceof RetryableException) {
      throw new BadGatewayException(serviceName);
    } else {
      throw new RemoteServiceException(extractedMessage, status);
    }
  }

  /**
   * If the body of exception is a JSON object and contains a "message" field, we will
   * extract it to a user-friendly error message.
   *
   * @param serviceName the name of the remote Service, which threw an error
   * @param ex the FeignException from which we extract the message
   * @return the extracted error message or a default string
   */
  private static String extractErrorMessageFromFeign(String serviceName, FeignException ex) {
    try {
      Optional<ByteBuffer> responseBody = ex.responseBody();
      if (responseBody.isPresent()) {
        String body = new String(responseBody.get().array(), StandardCharsets.UTF_8);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode json = mapper.readTree(body);
        if (json.has("message")) {
          return String.format(FORMAT, serviceName, json.get("message").asText());
        }
      }
    } catch (Exception e) {
      log.warn("Failed to parse error message from FeignException", e);
    }

    if (ex.status() == NOT_FOUND) {
      return String.format(FORMAT, serviceName, VEHICLE_NOT_FOUND_MSG);
    } else {
      return String.format(FORMAT, serviceName, DEFAULT_MSG);
    }
  }

}
