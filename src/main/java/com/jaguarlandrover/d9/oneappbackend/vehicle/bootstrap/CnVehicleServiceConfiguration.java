/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.bootstrap;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(
    name = "service.market",
    havingValue = "cn"
)
// Scan all packages for CN.
@ComponentScan(value = {
    "com.jaguarlandrover.d9.oneappbackend.vehicle.market.cn"
})
public class CnVehicleServiceConfiguration {

}
