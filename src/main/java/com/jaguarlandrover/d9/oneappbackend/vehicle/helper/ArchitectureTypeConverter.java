/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.helper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleArchitectureType;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.UnknownArchitectureException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ArchitectureTypeConverter {
  /**
   * Converts given string to a corresponding constant of vehicle architecture type.
   * @param type The string representing the architecture type to be converted to an enum
   * @throws UnknownArchitectureException if the provided string does not match any known architecture type
   */
  public static VehicleArchitectureType getArchitectureTypeFromString(String type) {
    try {
      return VehicleArchitectureType.valueOf(type);
    } catch (IllegalArgumentException e) {
      log.error("Unknown vehicle architecture type: {}", type);
      throw new UnknownArchitectureException("Unknown vehicle architecture type: " + type);
    }
  }
}
