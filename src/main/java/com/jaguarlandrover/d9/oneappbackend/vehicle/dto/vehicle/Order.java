/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Order {

  @Schema(example = "10001111")
  @JsonProperty("order_number")
  private String orderNumber;

  @Schema(example = "ON_WAY_TO_RETAILER")
  @JsonProperty("order_status")
  private String orderStatus;

  @Schema(example = "2023-04-07T00:00:00+0000")
  @JsonProperty("estimated_planned_delivery_date")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
  private LocalDateTime estimatedPlannedDeliveryDate;

  @Schema(example = "P4038R0626")
  @JsonProperty("dealership_reference_number")
  private String dealershipReferenceNumber;

  @Schema(example = "shanghai range rover 4S")
  @JsonProperty("dealership_name")
  private String dealershipName;

  @JsonProperty("dealership_contact_number")
  private String dealershipContactNumber;

  @JsonProperty("shipping_attributes")
  private ShippingAttributes shippingAttributes;

}
