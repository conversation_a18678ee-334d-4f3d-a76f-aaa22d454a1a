/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Feature {
  private String featureCode;
  private String description;
  private String featureLongDescription;
  private String featureName;
  private String featureAlternativeName;
  private String brandDescriptionLandRover;
  private String brandDescriptionJaguar;
  private String efgTypeName;
  private String wersFeatureCode;
  private String jlrFeatureFamilyType;
}
