/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VehicleOrder {

  private String vin;
  private String orderNumber;
  private OrderStatus orderStatus;
  private LocalDate plannedDeliveryDate;
  private String retailerReference;
  private String retailerName;
  private String dealershipContactNumber;
  private IncomingAttributes shippingAttributes;
}