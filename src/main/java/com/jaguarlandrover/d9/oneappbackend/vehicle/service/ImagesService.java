/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service;

import com.jaguarlandrover.d9.oneappbackend.vehicle.client.ImageClient;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.image.ImageResponse;
import com.jaguarlandrover.d9.oneappbackend.vehicle.repository.VehicleImages;
import com.jaguarlandrover.d9.oneappbackend.vehicle.repository.VehicleImagesRepository;
import feign.FeignException;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ImagesService {

  private static final String TOP = "TOP";

  private final VehicleImagesRepository vehicleImagesRepository;
  private final ImageClient imageClient;

  public ImagesService(VehicleImagesRepository vehicleImagesRepository, ImageClient imageClient) {
    this.vehicleImagesRepository = vehicleImagesRepository;
    this.imageClient = imageClient;
  }

  public Optional<String> findVehicleImageUrl(String model, String colour) {

    try {
      Optional<ImageResponse> imageResponseOptional = imageClient.getGenericImage(model, colour, TOP);

      if (imageResponseOptional.isPresent()) {
        log.info("Image received");
        if (imageResponseOptional.get().getUrl() != null) {
          return Optional.of(imageResponseOptional.get().getUrl());
        }
      }

      log.info("No image received");
      return Optional.empty();
    } catch (FeignException ex) {
      log.error("Error communicating with Images API: {} response for model {} and colour {}",
          ex.status(),
          model,
          colour);
      return Optional.empty();
    }
  }

  public Optional<String> findVehicleImageUrl(String orderNumber) {

    Optional<VehicleImages> vehicleImagesOptional = vehicleImagesRepository.findById(orderNumber);

    if (topImageFound(vehicleImagesOptional)) {
      return Optional.of(vehicleImagesOptional.get().getTopViewImageUrl());
    }
    log.info("Image not found in storage for order {}", orderNumber);


    // temporary solution - remove try/catch when image service in pre-prod and prod
    try {

      List<ImageResponse> imageResponseList = imageClient.getTopImageForOrderNumber(orderNumber,
          TOP);

      if (imageReturnedFromClient(imageResponseList)) {
        String url = imageResponseList.get(0).getUrl();
        vehicleImagesRepository.save(
            VehicleImages.builder().orderId(orderNumber).topViewImageUrl(url).build()
        );
        return Optional.of(url);
      }
      log.info("Image not returned from image service for order {}", orderNumber);

      return Optional.empty();

    } catch (FeignException ex) {
      log.error("Error communicating with Images API: {} response for order {}",
          ex.status(),
          orderNumber);
      return Optional.empty();
    }
  }

  private static boolean imageReturnedFromClient(List<ImageResponse> imageResponseList) {
    return imageResponseList != null && !imageResponseList.isEmpty();
  }

  private static boolean topImageFound(Optional<VehicleImages> vehicleImagesOptional) {
    return vehicleImagesOptional.isPresent()
        && vehicleImagesOptional.get().getTopViewImageUrl() != null;
  }
}
