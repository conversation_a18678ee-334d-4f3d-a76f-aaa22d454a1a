/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

@Component
public class OabLogInterceptor implements HandlerInterceptor {

  public static final String REQUEST_ID_HEADER_NAME = "x-amzn-RequestId";
  public static final String REQUEST_ID_PROPERTY_NAME = "http.request_id";

  @Override
  public boolean preHandle(
      HttpServletRequest request,
      HttpServletResponse response,
      Object handler) {

    String requestId = request.getHeader(REQUEST_ID_HEADER_NAME);

    if (requestId != null) {
      ThreadContext.put(REQUEST_ID_PROPERTY_NAME, requestId);
    }

    return true;
  }

  @Override
  public void postHandle(
      HttpServletRequest request,
      HttpServletResponse response,
      Object handler,
      ModelAndView modelAndView) {

  }

  @Override
  public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                              Object handler,
                              Exception exception) {
    ThreadContext.clearMap();
  }

}