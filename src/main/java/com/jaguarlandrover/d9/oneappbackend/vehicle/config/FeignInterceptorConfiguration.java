/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.config;

import feign.Feign;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignInterceptorConfiguration {

  @Bean
  public Feign feign() {
    return Feign.builder()
        .requestInterceptor(new FeignHeaderInterceptor())
        .build();
  }
}