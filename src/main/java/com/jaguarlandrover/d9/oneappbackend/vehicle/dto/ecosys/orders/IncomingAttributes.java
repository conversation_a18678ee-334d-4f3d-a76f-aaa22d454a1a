/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IncomingAttributes {

  private String plannedVesselName;
  private String actualVesselName;
  private LocalDateTime plannedSailDate;
  private Integer shippingBillOfLandingId;
  private LocalDateTime shippingBillOfLandingDate;
  private String shippingBillOfLadingStatus;
  private String intermediateDestinationId;
  private LocalDateTime plannedIntermediateDestinationDate;
  private LocalDateTime intermediateDestinationDate;
  private String finalDestinationId;
  private String finalDestinationDescription;
  private Integer finalDestinationOrgLevel;

}
