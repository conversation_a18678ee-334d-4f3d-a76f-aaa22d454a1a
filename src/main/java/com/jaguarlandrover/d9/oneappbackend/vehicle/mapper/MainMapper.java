/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.VehicleOrder;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class MainMapper {

  private final OrdersToVehiclesMapper ordersToVehiclesMapper;

  public MainMapper(OrdersToVehiclesMapper ordersToVehiclesMapper) {
    this.ordersToVehiclesMapper = ordersToVehiclesMapper;
  }

  public List<Vehicle> map(List<Vehicle> vehicles, List<VehicleOrder> orders) {

    List<Vehicle> orderList = ordersToVehiclesMapper.map(orders);
    List<Vehicle> resultList = new ArrayList<>(vehicles);

    orderList.forEach(order -> addToVehicleListIfNullOrUnique(order, vehicles, resultList));

    return resultList;
  }

  private void addToVehicleListIfNullOrUnique(Vehicle order,
                                              List<Vehicle> vehicles,
                                              List<Vehicle> resultList) {
    String orderVin = order.getVin();

    if (orderVin == null || noVehicleWithOrderVinInAttributes(vehicles, orderVin)) {
      resultList.add(order);
    }
  }

  private static boolean noVehicleWithOrderVinInAttributes(List<Vehicle> attributeList, String orderVin) {
    return attributeList.stream().noneMatch(v -> orderVin.equals(v.getVin()));
  }
}
