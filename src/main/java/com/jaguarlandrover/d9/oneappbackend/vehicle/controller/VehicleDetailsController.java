/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.controller;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle;
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.CounterService;
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.VehicleService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping(value = "/api/v1", produces = MediaType.APPLICATION_JSON_VALUE)
public class VehicleDetailsController {

  private final VehicleService vehicleService;
  private final CounterService counterService;

  /**
   * Constructor.
   */
  public VehicleDetailsController(VehicleService vehicleService,
                                  CounterService counterService) {
    this.vehicleService = vehicleService;
    this.counterService = counterService;
  }

  /**
   * Get Vehicle List by access token.
   *
   * @param token ForgeRock JWT Access Token
   * @return list of vehicles
   */
  @Tag(name = "Vehicle Details")
  @Operation(summary = "Get Vehicle List by access token",
      description = "Given a ForgeRock Access Token, get the list of vehicles of the user.")
  @ApiResponses(value = {
      @ApiResponse(
          responseCode = "200",
          description = "List of vehicle data received.",
          content = @Content(
              array = @ArraySchema(schema = @Schema(implementation = Vehicle.class)))),
      @ApiResponse(
          responseCode = "401",
          description = "Unauthorized to access.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "403",
          description = "Access to vehicles is forbidden with the given access token.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "404",
          description = "Vehicles not found for given access token.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "500",
          description = "Internal Server Error.",
          content = @Content(schema = @Schema())),
      @ApiResponse(
          responseCode = "502",
          description = "Bad Gateway. Failed to connect to upstream server.",
          content = @Content(schema = @Schema()))
  })
  @Timed(value = "oab.vehicle.request.get.measured")
  @GetMapping(value = "/users/me/vehicles")
  public ResponseEntity<List<Vehicle>> getVehicleDetails(
      @Parameter(description = "ForgeRock JWT Access Token")
      @RequestHeader(name = "Authorization") String token) {

    log.info("Received request for vehicles");
    counterService.getRequestCounter().increment();

    return ResponseEntity.ok(vehicleService.getVehicleDetails(token));
  }

}
