/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service;

import com.jaguarlandrover.d9.oneappbackend.springsecurity.provider.UserDataProvider;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.ForbiddenException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RegistrationUpdateValidator {

  private final UserDataProvider userDataProvider;

  public RegistrationUpdateValidator(UserDataProvider userDataProvider) {
    this.userDataProvider = userDataProvider;
  }

  boolean isValid(String vehicleId) {
    if (userDataProvider.getOabUserData().getVehicles().contains(vehicleId)) {
      return true;
    } else {
      String message = "VehicleId provided is not present in the user's FR token entitlement";
      log.error(message);
      throw new ForbiddenException(message);
    }
  }
}
