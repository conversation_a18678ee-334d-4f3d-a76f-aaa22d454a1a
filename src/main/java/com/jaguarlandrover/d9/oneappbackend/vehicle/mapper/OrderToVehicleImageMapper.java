/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Images;
import com.jaguarlandrover.d9.oneappbackend.vehicle.service.ImagesService;
import org.springframework.stereotype.Component;

@Component
public class OrderToVehicleImageMapper {

  private final ImagesService imagesService;

  public OrderToVehicleImageMapper(ImagesService imagesService) {
    this.imagesService = imagesService;
  }

  public Images map(String orderNo) {
    return imagesService.findVehicleImageUrl(orderNo)
        .map(s -> Images.builder().primary(s).build())
        .orElse(Images.empty());
  }

}
