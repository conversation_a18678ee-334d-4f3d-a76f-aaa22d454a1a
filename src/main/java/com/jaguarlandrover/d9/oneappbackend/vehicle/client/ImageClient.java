/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.client;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.image.ImageResponse;
import io.micrometer.core.annotation.Timed;
import java.util.List;
import java.util.Optional;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ecosys-image-service-client",
    url = "${ecosys-image-service.base-url}")
public interface ImageClient {

  @Timed(value = "oab.vehicle.image.request.measured")
  @GetMapping(value = "/api/v1/images/{orderNumber}")
  List<ImageResponse> getTopImageForOrderNumber(@PathVariable String orderNumber,
                                                @RequestParam String viewAngle);

  // remove when tests done
  @Timed(value = "oab.vehicle.image.request.measured")
  @GetMapping(value = "/api/v1/images/generic")
  Optional<ImageResponse> getGenericImage(@RequestParam String model,
                                          @RequestParam String colour,
                                          @RequestParam String viewAngle);

}
