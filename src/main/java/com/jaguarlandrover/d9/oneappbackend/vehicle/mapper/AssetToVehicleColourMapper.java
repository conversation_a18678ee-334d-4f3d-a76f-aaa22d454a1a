/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Feature;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class AssetToVehicleColourMapper {

  public static final String COLOUR_EFG_TYPE_NAME = "PCOL";
  public static final String JAGUAR_BRAND_NAME = "JAG";

  public String map(Assets assets) {
    if (assets.getFeatures() != null) {
      Optional<Feature> featureOptional = assets.getFeatures().stream()
          .filter(e -> e.getEfgTypeName() != null
              && e.getEfgTypeName().contains(COLOUR_EFG_TYPE_NAME))
          .findAny();
      return featureOptional.map(feature -> getBrandDescription(assets, feature)).orElse(null);
    } else {
      return null;
    }
  }

  private String getBrandDescription(Assets assets, Feature feature) {
    String brand = assets.getBrand();
    if (JAGUAR_BRAND_NAME.equalsIgnoreCase(brand)) {
      return feature.getBrandDescriptionJaguar();
    } else {
      return feature.getBrandDescriptionLandRover();
    }
  }
}
