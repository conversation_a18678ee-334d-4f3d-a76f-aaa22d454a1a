/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.config;

import feign.Client;
import feign.okhttp.OkHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;

@Slf4j
public class FeignOkHttpConfiguration {

  @Bean
  public Client client() {

    OkHttpClient okHttpClient = new OkHttpClient();
    String name = okHttpClient.getClass().getName();
    log.info("Created Client: " + name);

    return okHttpClient;
  }

}
