/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.ArchitectureTypeConverter.getArchitectureTypeFromString;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Assets;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.Identity;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleArchitectureType;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.UnknownArchitectureException;
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OwnershipType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AssetsToVehicleMapper {

  private final AssetToVehicleModelDescriptionMapper assetToVehicleModelDescriptionMapper;
  private final AssetToVehicleDisplayNameMapper assetToVehicleDisplayNameMapper;
  private final AssetToVehicleFuelTypeMapper assetToVehicleFuelTypeMapper;
  private final AssetToVehicleColourMapper assetToVehicleColourMapper;
  private final AssetToVehicleImageMapper assetToVehicleImageMapper;

  public AssetsToVehicleMapper(AssetToVehicleModelDescriptionMapper assetToVehicleModelDescriptionMapper,
                               AssetToVehicleDisplayNameMapper assetToVehicleDisplayNameMapper,
                               AssetToVehicleFuelTypeMapper assetToVehicleFuelTypeMapper,
                               AssetToVehicleColourMapper assetToVehicleColourMapper,
                               AssetToVehicleImageMapper assetToVehicleImageMapper) {
    this.assetToVehicleModelDescriptionMapper = assetToVehicleModelDescriptionMapper;
    this.assetToVehicleDisplayNameMapper = assetToVehicleDisplayNameMapper;
    this.assetToVehicleFuelTypeMapper = assetToVehicleFuelTypeMapper;
    this.assetToVehicleColourMapper = assetToVehicleColourMapper;
    this.assetToVehicleImageMapper = assetToVehicleImageMapper;
  }

  public Vehicle map(VehicleDataResponse vehicleDataResponse) {
    if (vehicleDataResponse == null || vehicleDataResponse.getData() == null) {
      return null;
    }

    Assets assets = vehicleDataResponse.getData().getAssets();
    Identity identity = vehicleDataResponse.getData().getIdentity();

    if (assets == null || identity == null) {
      return null;
    }

    return Vehicle.builder()
            .vid(identity.getUniqueId())
            .vin(identity.getVin())
            .architecture(getArchitecture(assets.getVehicleArchitecture()))
            .brand(assets.getBrand())
            .model(assets.getModelRange())
            .modelName(assets.getModelName())
            .modelYear(assets.getModelYear())
            .description(assetToVehicleModelDescriptionMapper.map(assets))
            .colour(assetToVehicleColourMapper.map(assets))
            .ownershipType(OwnershipType.PRIMARY_OWNED.name())
            .images(assetToVehicleImageMapper.map(assets))
            .registration(identity.getVehicleReg())
            .fuelType(assetToVehicleFuelTypeMapper.map(assets))
            .displayName(assetToVehicleDisplayNameMapper.map(assets, identity))
            .build();
  }

  /**
   * Calls converting method to get a vehicle architecture type.
   * @param type The string representing the architecture type from assets
   * @return null if caught UnknownArchitectureException
   */
  private static VehicleArchitectureType getArchitecture(String type) {
    try {
      return getArchitectureTypeFromString(type);
    } catch (UnknownArchitectureException e) {
      return null;
    }
  }
}
