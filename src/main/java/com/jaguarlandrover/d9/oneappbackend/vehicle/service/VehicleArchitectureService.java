/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service;

import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.ArchitectureTypeConverter.getArchitectureTypeFromString;
import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.ExceptionHandler.handleFeignException;
import static com.jaguarlandrover.d9.oneappbackend.vehicle.helper.IncludeConstants.ASSETS;

import com.jaguarlandrover.d9.oneappbackend.vehicle.client.DigitalVehicleClient;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleArchitectureType;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleDataResponse;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.DigitalVehicleException;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class VehicleArchitectureService {

  private final DigitalVehicleClient digitalVehicleClient;

  public VehicleArchitectureService(DigitalVehicleClient digitalVehicleClient) {
    this.digitalVehicleClient = digitalVehicleClient;
  }

  /**
   * Retrieves Vehicle Architecture type from Digital Vehicle API.
   * @param token ForgeRock JWT access token
   * @param vehicleId vehicle ID
   * @throws DigitalVehicleException if data is missing or architecture type is null
   */
  public VehicleArchitectureType getVehicleArchitecture(String token, String vehicleId) {

    try {
      VehicleDataResponse response = digitalVehicleClient.getVehicleData(token, vehicleId, ASSETS);

      if (response == null || response.getData() == null || response.getData().getAssets() == null) {
        log.error("Vehicle architecture data is missing for vehicle ID: {}", vehicleId);
        throw new DigitalVehicleException("Vehicle architecture data is missing");
      }

      String architectureTypeReturned = response.getData().getAssets().getVehicleArchitecture();

      if (architectureTypeReturned == null) {
        log.error("Vehicle architecture type is null for vehicle ID: {}", vehicleId);
        throw new DigitalVehicleException("Vehicle architecture type is null for vehicle ID: " + vehicleId);
      }

      return getArchitectureTypeFromString(architectureTypeReturned);

    } catch (FeignException ex) {
      log.error("Digital Vehicle API error: {}", ex.getMessage());
      handleFeignException("Digital Vehicle API", ex);
    }
    return null;
  }
}
