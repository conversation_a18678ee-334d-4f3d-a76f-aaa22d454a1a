/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.fr;

import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
public class FrJwk {

  // Key Type
  private String kty;

  // Key Identifier
  private String kid;

  // Usage – ‘sig’ for signing keys, ‘enc’ for encryption keys
  private String use;

  //  X.509 Certificate Thumbprint
  private String x5t;

  // X.509 Certificate Chain
  private List<String> x5c;

  private String n;

  private String e;

}
