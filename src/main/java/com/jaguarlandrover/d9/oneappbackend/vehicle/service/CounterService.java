/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Getter;
import org.springframework.stereotype.Service;

@Getter
@Service
public class CounterService {

  public static final String OAB_VEHICLE_REQUESTS_RECEIVED
      = "oab.vehicle.request.received";
  public static final String OAB_VEHICLE_REQUESTS_ERRORS
      = "oab.vehicle.request.error";
  public static final String OAB_VEHICLE_REQUEST_NO_VEHICLES_IN_TOKEN
      = "oab.vehicle.request.no.vehicles.in.token";
  public static final String OAB_VEHICLES_RETRIEVED
          = "oab.vehicles.retrieved";

  private final Counter requestCounter;
  private final Counter errorCounter;
  private final Counter noVehiclesInTokenCounter;
  private final Counter vehiclesRetrievedCounter;

  public CounterService(MeterRegistry meterRegistry) {
    requestCounter = meterRegistry.counter(OAB_VEHICLE_REQUESTS_RECEIVED);
    errorCounter = meterRegistry.counter(OAB_VEHICLE_REQUESTS_ERRORS);
    noVehiclesInTokenCounter = meterRegistry.counter(OAB_VEHICLE_REQUEST_NO_VEHICLES_IN_TOKEN);
    vehiclesRetrievedCounter = meterRegistry.counter(OAB_VEHICLES_RETRIEVED);
  }
}
