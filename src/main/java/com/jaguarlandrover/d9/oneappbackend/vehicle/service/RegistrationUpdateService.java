/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.service;

import com.jaguarlandrover.d9.oneappbackend.vehicle.client.DigitalVehicleClient;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.digitalvehicle.VehicleRegistrationUpdateRequest;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.RegistrationUpdateRequest;
import com.jaguarlandrover.d9.oneappbackend.vehicle.exception.BadGatewayException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@Slf4j
@Service
public class RegistrationUpdateService {

  private final DigitalVehicleClient client;
  private final RegistrationUpdateValidator registrationUpdateValidator;

  public RegistrationUpdateService(DigitalVehicleClient client,
                                   RegistrationUpdateValidator registrationUpdateValidator) {
    this.client = client;
    this.registrationUpdateValidator = registrationUpdateValidator;
  }

  /**
   * Update Vehicle Registration.
   * @param token ForgeRock JWT access token
   * @param vehicleId vehicle ID
   * @param registrationUpdateRequest registration to be updated
   */
  public void updateVehicleRegistration(
      @RequestHeader(name = "Authorization") String token,
      @PathVariable String vehicleId,
      @RequestBody RegistrationUpdateRequest registrationUpdateRequest) {
    log.info("Started updating Registration No");
    if (registrationUpdateValidator.isValid(vehicleId)) {
      try {
        client.updateVehicleRegistration(token, vehicleId,
            new VehicleRegistrationUpdateRequest(registrationUpdateRequest.getRegistration()));
      } catch (RetryableException ex) {
        throw new BadGatewayException("Digital Vehicle API");
      } finally {
        log.info("Finished updating Registration No");
      }
    }
  }
}
