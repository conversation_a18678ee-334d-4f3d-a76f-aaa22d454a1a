/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.client;

import com.jaguarlandrover.d9.oneappbackend.vehicle.config.FeignOkHttpConfiguration;
import io.micrometer.core.annotation.Timed;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(value = "ecosys-wireless-car-glue-wrapper-client",
    url = "${ecosys-tsdp-wrapper-service.base-url}",
    configuration = FeignOkHttpConfiguration.class)
public interface WirelessCarGlueWrapperClient {

  @Timed(value = "oab.vehicle.wirelesscar.request.delete.measured")
  @DeleteMapping(value = "/api/v1/users/me/vehicles/{vehicleId}")
  ResponseEntity<Void> removeVehicleById(
      @RequestHeader("Authorization") String token,
      @PathVariable("vehicleId") String vehicleId
  );
}
