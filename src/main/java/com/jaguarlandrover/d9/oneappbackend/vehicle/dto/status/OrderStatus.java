/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.dto.status;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Vehicle Order Status in this Vehicle Service.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderStatus {

  private List<Integer> code;
  private String status;
}
