/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.vehicle.mapper;

import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.IncomingAttributes;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.ecosys.orders.VehicleOrder;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Order;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.ShippingAttributes;
import com.jaguarlandrover.d9.oneappbackend.vehicle.dto.vehicle.Vehicle;
import com.jaguarlandrover.d9.oneappbackend.vehicle.helper.OwnershipType;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrdersToVehiclesMapper {

  private final OrderToVehicleImageMapper orderToVehicleImageMapper;

  public OrdersToVehiclesMapper(OrderToVehicleImageMapper orderToVehicleImageMapper) {
    this.orderToVehicleImageMapper = orderToVehicleImageMapper;
  }

  public List<Vehicle> map(@NotNull List<VehicleOrder> orders) {
    return orders.stream()
        .map(this::orderToVehicle)
        .toList();
  }

  private Vehicle orderToVehicle(VehicleOrder vehicleOrder) {
    String orderNumber = vehicleOrder.getOrderNumber();

    return Vehicle.builder()
        .vin(vehicleOrder.getVin())
        .ownershipType(OwnershipType.ORDERED.name())
        .fuelType(Vehicle.FuelType.UNKNOWN)
        .displayName(orderNumber)
        .images(orderToVehicleImageMapper.map(orderNumber))
        .order(createOrder(vehicleOrder))
        .build();
  }

  private Order createOrder(VehicleOrder vehicleOrder) {
    return Order.builder()
        .orderNumber(vehicleOrder.getOrderNumber())
        .orderStatus(vehicleOrder.getOrderStatus().toString())
        .estimatedPlannedDeliveryDate(
            convertToLocalDateTime(vehicleOrder.getPlannedDeliveryDate()))
        .dealershipReferenceNumber(vehicleOrder.getRetailerReference())
        .dealershipName(vehicleOrder.getRetailerName())
        .dealershipContactNumber(vehicleOrder.getDealershipContactNumber())
        .shippingAttributes(createShippingAttributes(vehicleOrder.getShippingAttributes()))
        .build();
  }

  private ShippingAttributes createShippingAttributes(IncomingAttributes incomingAttributes) {
    return ShippingAttributes.builder()
        .plannedVesselName(incomingAttributes.getPlannedVesselName())
        .actualVesselName(incomingAttributes.getActualVesselName())
        .plannedSailDate(incomingAttributes.getPlannedSailDate())
        .shippingBillOfLandingId(incomingAttributes.getShippingBillOfLandingId())
        .shippingBillOfLandingDate(incomingAttributes.getShippingBillOfLandingDate())
        .shippingBillOfLadingStatus(incomingAttributes.getShippingBillOfLadingStatus())
        .intermediateDestinationId(incomingAttributes.getIntermediateDestinationId())
        .plannedIntermediateDestinationDate(incomingAttributes.getPlannedIntermediateDestinationDate())
        .intermediateDestinationDate(incomingAttributes.getIntermediateDestinationDate())
        .finalDestinationId(incomingAttributes.getFinalDestinationId())
        .finalDestinationDescription(incomingAttributes.getFinalDestinationDescription())
        .finalDestinationOrgLevel(incomingAttributes.getFinalDestinationOrgLevel())
        .build();
  }

  private LocalDateTime convertToLocalDateTime(LocalDate localDate) {
    return localDate == null ? null : LocalDateTime.of(localDate, LocalTime.MIN);
  }

}
