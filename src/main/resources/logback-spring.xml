<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <!-- Log messages in plain text -->
    <appender name="Console"
              class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{ISO8601} %highlight(%-5level) [%blue(%t)] %yellow(%C): %msg%n%throwable %green(x-amzn-RequestId=%X{http.request_id}){}%n
            </Pattern>
        </layout>
    </appender>

    <!-- Log messages in JSON format -->
    <appender name="ConsoleInJson"
              class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <springProfile name="default | local">
        <root level="info">
            <appender-ref ref="Console"/>
        </root>
    </springProfile>

    <springProfile name="!(default | local)">
        <root level="info">
            <appender-ref ref="ConsoleInJson"/>
        </root>
    </springProfile>

</configuration>
