server:
  port: ${SERVER_PORT:8080}
  max-http-request-header-size: 32KB

spring:
  application:
    name: oab-vehicle-service
  config:
    import: "optional:aws-secretsmanager:oab/vehicle"
  cloud:
    aws:
      region:
        static: eu-west-2
        auto: false

management:
  endpoints:
    web:
      exposure:
        include: health,refresh
  endpoint:
    health:
      probes:
        enabled: true
  datadog:
    metrics:
      export:
        uri: "https://datadoghq.eu"
        api-key: ${DATADOG_API_KEY:fake-api-key}

logging:
  level:
    com.jaguarlandrover.d9.oneappbackend.vehicle: DEBUG

#
# Defined by our application
#
app:
  config:
    modelDescription:
      L460: Range Rover
      L461: Range Rover Sport

order:
  config:
    responseFlag: true

oab:
  security:
    forge-rock:
      base-url: ${FORGE_ROCK_BASE_URL:http://localhost:8083}

forge-rock:
  base-url: ${FORGE_ROCK_BASE_URL:http://localhost:8083}

ecosys-image-service:
  base-url: ${IMAGE_URL:http://localhost:8084}

ecosys-order-service:
  base-url: ${ORDER_URL:http://localhost:8082}

ecosys-tsdp-wrapper-service:
  base-url: http://localhost:8086

digital-vehicle:
  base-url: http://localhost:8085

monitoring:
  metrics:
    tags:
      env: ${DD_ENV:local}
      service: ${DD_SERVICE:oab-vehicle-service}
      version: ${DD_VERSION:default}
