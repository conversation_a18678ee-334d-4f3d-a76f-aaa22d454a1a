{"rules": {"body-leading-blank": [1, "always"], "footer-leading-blank": [1, "always"], "header-max-length": [2, "always", 72], "scope-case": [2, "always", "upper-case"], "scope-empty": [2, "never"], "subject-case": [2, "never", ["start-case", "pascal-case", "upper-case"]], "subject-empty": [2, "never"], "subject-full-stop": [2, "never", "."], "type-case": [2, "always", "lower-case"], "type-empty": [2, "never"], "type-enum": [2, "always", ["build", "chore", "ci", "docs", "feat", "fix", "perf", "refactor", "revert", "style", "test"]]}}