include:
  - project: 'D9/infrastructure/gitlab-ci-templates'
    ref: master
    file: '.k8s-maven-gitlab-ci-template.yml'
  - project: 'd9/infrastructure/gitlab-ci-templates'
    ref: master
    file: '.k8s-maven-gitlab-ci-template-cn.patch.yml'
    inputs:
      deploy_aws_dev_cn: true
      deploy_aws_preprod_cn: true
      deploy_aws_prod_cn: true
      k8s_folder_prefix_cn: mab-cn


# comment out this variable if you want to deploy to vcdp-developers account instead of mobile-apps-backend-developers
variables:
  K8S_FOLDER_PREFIX: mab
  BASE_JAVA_JOB_IMAGE: docker-hub/library/maven:3.9.8-amazoncorretto-21

install_dependencies_cn:
  image: "${REGISTRY}/maven:3.9.6-amazoncorretto-21"
  tags:
    - mobile-apps-backend-cn-dev

build_image_aws:
  tags:
    - aws
    - vcdp-dev

build_image_aws_cn:
  variables:
    BASE_JAVA_JOB_IMAGE: "maven:3.9.6-amazoncorretto-21"
  tags:
    - mobile-apps-backend-cn-dev

tests:
  tags:
    - aws-ec2-shell-vcdp-developers
  variables:
    JDK_VERSION: "21"
    JDK_DOWNLOAD_URL: "https://download.oracle.com/java/21/latest/jdk-21_linux-x64_bin.tar.gz"
    JDK_INSTALL_PATH: "/tmp/jdk${JDK_VERSION}"
  before_script:
    - wget --no-check-certificate -c "$JDK_DOWNLOAD_URL" -O jdk-${JDK_VERSION}.tar.gz
    - mkdir -p $JDK_INSTALL_PATH
    - tar -xzf jdk-${JDK_VERSION}.tar.gz -C $JDK_INSTALL_PATH --strip-components=1
    - export JAVA_HOME=$JDK_INSTALL_PATH
    - export PATH=$JAVA_HOME/bin:$PATH

######################################################################
# PreProd         - run preprod automation tests #
######################################################################
stages:
  - install
  - validate
  - mutate
  - inspect code
  - config validation
  - docker build
  - inspect image
  - dev
  - dev test
  - staging
  - preprod
  - preprod integration testing
  - preprod test
  - production
  - production test
  - trigger_datadog

preprod_integration_testing:
  stage: preprod integration testing
  allow_failure: false
  trigger:
    project: D9/one-app/automated-tests/oab-vehicle-service-automation-tests
    branch: main
    strategy: depend
  rules:
    - if: $CI_COMMIT_REF_NAME != $CI_DEFAULT_BRANCH
      when: never
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: on_success

preprod_integration_testing_cn:
  stage: preprod integration testing
  allow_failure: false
  trigger:
    project: D9/one-app/automated-tests/oab-vehicle-service-automation-tests
    branch: dev_cn
    strategy: depend
  rules:
    - if: $CI_COMMIT_REF_NAME != $CI_DEFAULT_BRANCH
      when: never
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: on_success