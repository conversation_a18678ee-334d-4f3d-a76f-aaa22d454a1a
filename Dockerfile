FROM 557068259488.dkr.ecr.eu-west-2.amazonaws.com/docker-hub/library/amazoncorretto:21-alpine3.21

LABEL maintainer="epopoola" version="0.1"

RUN addgroup -g 2000 -S appgroup && adduser -u 1000 -S appuser -G appgroup

WORKDIR /opt/application

RUN wget -O dd-java-agent.jar 'https://dtdg.co/latest-java-tracer'

COPY target/oab-vehicle-service*.jar app.jar

RUN chown -R 1000:2000 /opt/application

USER 1000

ENTRYPOINT ["java","-javaagent:./dd-java-agent.jar","-Djava.security.egd=file:/dev/./urandom","-Djava.net.preferIPv4Stack=true","-jar", "app.jar"]

EXPOSE 8080
