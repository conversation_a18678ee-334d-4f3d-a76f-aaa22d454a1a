# One App Backend Vehicle Service #

[Sonar Badge]


## Overview ##
This One App backend service is used for retrieving vehicle details against provided contact id

## Documentation ##
[openapi.json](../openapi/openapi.json)

## Secrets Manager ##

* Create secret in AWS Secrets Manager using AWS CLI.

```shell
export AWS_PROFILE_NAME=mab
export SECRET_NAME="oab/vehicle"
export SECRET_DESC="Secrets to be used by OneApp Backend Vehicle Service."
export DATADOG_API_KEY=datadog-api-key
export SECRET_STRING="{\"management.datadog.metrics.export.api-key\":\"DATADOG_API_KEY\"}"
echo $SECRET_STRING

aws secretsmanager create-secret --name $SECRET_NAME \
--description "$SECRET_DESC" \
--secret-string "$SECRET_STRING" --profile $AWS_PROFILE_NAME
```

* Get secret value.

```shell
aws secretsmanager get-secret-value --secret-id $SECRET_NAME --profile $AWS_PROFILE_NAME
```

* Update secret value.

```shell
aws secretsmanager update-secret --secret-id $SECRET_NAME \
--description "$SECRET_DESC" \
--secret-string "$SECRET_STRING" --profile $AWS_PROFILE_NAME
```

## Clean install ##
To clean and build your project you can run the following command.
The -U will force update of maven dependencies. It will also start the application temporarily and generate your swagger docs
```shell
mvn clean install -U -Dmaven.skip.test=true -Ddependency-check.skip=true
```

If you run your application you can access the swagger docs at
http://localhost:8080/swagger-ui.html

If you want to regenerate the swagger docs, keep your application running and run the below command.
This will generate a file inside the folder openapi at the root of your project. The clean install command
```shell
mvn springdoc-openapi:generate
```

## Start the application locally ##

You can run application locally by simply clicking on the main method in the VehicleServiceApplication class

OR

run the below command in terminal.
```shell
mvn spring-boot:run -Dspring-boot.run.arguments="\
--spring.profiles.active=local"
```

The above command imports the properties file named `/src/main/resources/local.env.properties`.  Create the file locally, and replace with desired values.

```properties
FORGE_ROCK_BASE_URL=https://qa.identity.jaguarlandrover.com
ATTRIBUTE_BASE_URL=https://vehicle-attribute-gateway.dev.jlr-vcdp.com
IMAGE_URL=https://image-service-gateway.eco-integration.dev.jlr-vcdp.com
ORDER_URL=https://oab-order-service.mab.dev.jlr-vcdp.com.
DATADOG_API_KEY=dd-api-key-value
```

## In case of using secrets from aws acct when starting the application locally try following: ##
Grab the AWS acct credentials and run the following cmds on the same terminal on root proj:

```shell
export AWS_ACCESS_KEY_ID="<YOUR_AWS_ACCESS_KEY_ID>"
export AWS_SECRET_ACCESS_KEY="<YOUR_AWS_SECRET_ACCESS_KEY>"
export AWS_SESSION_TOKEN="<YOUR_AWS_SESSION_TOKEN>"
mvn spring-boot:run
```

## Code coverage report ##
If you want to generate the JaCoCo code coverage report, run the below command.
This will run all the tests and generate a report file, skipping the slow dependency checking.
```shell
mvn verify -Ddependency-check.skip
```
To see the code coverage report open `target/site/jacoco/index.html` on your web browser.

## Run unit test and integration test ##
If you want to generate the JaCoCo code coverage report, run the below command.
This will run all the tests and generate a report file, skipping the slow dependency checking.
```shell
mvn test
mvn failsafe:integration-test
```


## Checkstyle report ##
If you want to see the checkstyle report on the terminal, run the below command.
```shell
mvn checkstyle:check
```

## Mutation Test##
If you want to run mutation tests, run the below command which will generate a report
```shell
mvn org.pitest:pitest-maven:mutationCoverage
```
The reports are generated in target/pit-reports/date/index.html


## Note ##
Please find, replace & change the name of the project to you're appropriate service name. Update the readme to correspond with you're service & also change the port number to something appropriate for your service.

## License and copyright ##

Copyright (c) 2022. Jaguar Land Rover - All Rights Reserved.

CONFIDENTIAL INFORMATION - DO NOT DISTRIBUTE
